name: 'PR title validation, labeling and review request Slack message'

on:
  pull_request:
    types: [opened, edited, ready_for_review, reopened]

jobs:
  main:
    if: github.event.pull_request.draft == false
    name: Validate PR title and label the PR
    runs-on: ubuntu-22.04
    steps:
      - name: Validate PR title
        uses: BareSquare/action-semantic-pull-request@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # https://github.com/commitizen/conventional-commit-types/blob/master/index.json
          types: |
            [feat]
            [fix]
            [docs]
            [style]
            [refactor]
            [perf]
            [test]
            [build]
            [ci]
            [chore]
            [revert]
            [sec]
          scopes: |
            INSIGHTS-\d+
            PL-\d+
            ADHOC
            Snyk
          requireScope: true
          subjectPattern: ^[A-Z].*$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            starts with an uppercase character.
          headerPattern: '^(\[\w*\]) (?:([\w\$\.\-\* ]*))?\: (.*)$'
          headerPatternCorrespondence: type, scope, subject

      - name: Label the PR
        uses: BareSquare/pr-prefix-labeler@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
