# Node
PORT=3003

# Auth0
INSIGHTS_BFF_URL=http://localhost:3002
BE_APP_AUTH0_AUDIENCE=https://insights-app-be.us-east-1.test.baresquare.com
FLOW_API_AUTH0_AUDIENCE=https://flow-management.us-east-1.test.baresquare.com/
AUTH0_DOMAIN=dev-f25-2j35.us.auth0.com
AUTH0_CLAIMS_NAMESPACE=https://baresquare.com

INSIGHTS_APP_API_AUTH0_AUDIENCE=https://insights-app.test.baresquare.com

# Database
# DB_HOST=insights-app-db # Use when the BE database runs without Docker
DB_HOST=ia-be-db          # Use when the BE database runs with Docker
DB_NAME=insights
DB_USERNAME=
DB_PASSWORD=
DB_PORT=5432
DB_HOST_PORT=5433

# AI
OPENAI_API_KEY=
GROQ_API_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=https://cloud.langfuse.com

# Redis
# To run the app without Docker Compose, start a Redis Docker container with command:
# docker run --rm -d --network host --name redis redis:7-alpine
# REDIS_URL=redis://localhost:6379/3
# To run the app with Docker Compose:
# REDIS_URL=redis://redis:6379/3
# REDIS_PASSWORD=
# To use the TEST env Redis, you need to be connected to the VPN
# Non existing URL anymore. We also use rediss. ->  REDIS_URL=redis://collaboration-jobs-dbms.us-east-1.test.baresquare.com:6379/3

LOGGER_LEVEL=info

# Docker
DOCKER_BUILD=local_build
DOCKER_PORT=3003
