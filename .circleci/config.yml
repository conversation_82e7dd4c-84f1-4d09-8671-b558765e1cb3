version: 2.1

setup: true

orbs:
  path-filtering: circleci/path-filtering@1.1
  continuation: circleci/continuation@1.0

parameters:
  # Releases
  trigger_prod_release:
    type: boolean
    default: false
  release_target_sha:
    type: string
    default: ''

workflows:
  version: 2
  setup:
    unless: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - path-filtering/filter:
          name: check-updated-files
          base-revision: main
          config-path: .circleci/continue_config.yml
          mapping: |
            src/.* application true
            test/.* application true
            tsconfig.* application true
            jest.* application true
            .dockerignore  application true
            docker-compose.yml application true
            docker/.* application true
            lambda.mjs application true
            package.* application true
            contracts/.* application true
            terraform/test/.* tf-test true
            terraform/prod/.* tf-prod true
          filters:
            tags:
              only:
                - /^prod-.*/
  setup-manual:
    when: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - continuation/continue:
          name: continue-manual-trigger
          configuration_path: .circleci/continue_config.yml
