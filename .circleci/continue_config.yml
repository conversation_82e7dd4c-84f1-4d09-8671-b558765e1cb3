version: 2.1

orbs:
  tf: baresquare/terraform@1.4
  releases: baresquare/releases@1.14
  jira: circleci/jira@2.1

### PARAMETERS ###
parameters:
  application:
    type: boolean
    default: false
  # Terraform
  tf-test:
    type: boolean
    default: false
  tf-prod:
    type: boolean
    default: false
  # Releases
  trigger_prod_release:
    type: boolean
    default: false
  release_target_sha:
    type: string
    default: ''

### WORKFLOWS ###
workflows:
  feature_branches:
    jobs:
      - releases/feature_branch_testing:
          name: 'feature_branch_testing'
          checkout_submodules: true
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          perform_linting: << pipeline.parameters.application >>
          perform_testing: << pipeline.parameters.application >>
          slack_notification_type: 'FAILURE_ONLY'
          service_name: 'insights_app_be'
          filters:
            branches:
              ignore: main
            tags:
              ignore: /.*/
  test_env:
    when: << pipeline.parameters.application >>
    jobs:
      - releases/build_tagged:
          name: 'build_test'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          aws_iam_role: 'baresquare_service_insights_app_role'
          checkout_submodules: true
          service_name: 'insights_app_be'
          dockerfile: 'docker/Dockerfile'
          env: 'TEST'
          git_branch_name: 'main'
          git_tag_name: ''
          build_target: 'lambda_build'
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
      - releases/deploy_tagged:
          name: 'deploy_test'
          requires:
            - build_test
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          aws_iam_role: 'baresquare_service_insights_app_role'
          service_name: 'insights_app_be'
          env: 'TEST'
          git_tag_name: ''
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          cloud_service: 'aws-lambda-provisioned'
          slack_notification_type: 'FAILURE_ONLY'
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
  prod_env:
    jobs:
      - releases/build_tagged:
          name: 'build_prod'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          aws_iam_role: 'baresquare_service_insights_app_role'
          checkout_submodules: true
          dockerfile: 'docker/Dockerfile'
          service_name: 'insights_app_be'
          env: 'PROD'
          git_branch_name: 'main'
          git_tag_name: << pipeline.git.tag >>
          build_target: 'lambda_build'
          working_directory: '~/project'
          filters:
            tags:
              only: /^prod-.*/
            branches:
              ignore: /.*/
      - releases/deploy_tagged:
          name: 'deploy_prod'
          requires:
            - build_prod
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          aws_iam_role: 'baresquare_service_insights_app_role'
          service_name: 'insights_app_be'
          env: 'PROD'
          git_tag_name: << pipeline.git.tag >>
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          cloud_service: 'aws-lambda-provisioned'
          slack_notification_type: 'FAILURE_ONLY'
          filters:
            tags:
              only: /^prod-.*/
            branches:
              ignore: /.*/
  release_to_prod:
    when: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - releases/push_release_tag:
          env: 'PROD'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          release_target_sha: << pipeline.parameters.release_target_sha >>
          tag_prefix_prod: 'prod-r'
          tag_version: 'sequence'
          has_stage_env: false
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
  ### Terraform ###
  terraform-test:
    when: << pipeline.parameters.tf-test >>
    jobs:
      - tf/execute:
          name: aws-us-test
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          tf_path: 'terraform/test'
          env: 'TEST'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            tags:
              ignore: /.*/
  terraform-prod:
    when: << pipeline.parameters.tf-prod >>
    jobs:
      - tf/execute:
          name: aws-us-prod
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          tf_path: 'terraform/prod'
          env: 'PROD'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            tags:
              ignore: /.*/
  ###
  no-changes:
    when:
      not:
        or:
          - << pipeline.parameters.application >>
          - << pipeline.parameters.tf-test >>
          - << pipeline.parameters.tf-prod >>
          - << pipeline.parameters.trigger_prod_release >>
    jobs:
      - tf/do_nothing:
          name: do-nothing
          filters:
            branches:
              only: main
