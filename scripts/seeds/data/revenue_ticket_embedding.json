{"revenue_ticket_embedding": [{"revenue_ticket_id": 1, "revenue_ticket_field": "category", "text_value": "Outlet Bottoms", "embedding": "[-0.06640531,-0.008711198,-0.012462244]", "platform": "openai", "model": "text-embedding-3-large", "cost": 3.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv1ExecutiveSummary", "text_value": "'Outlet Bottoms'Orders impacted Revenue Focus on 'Paid Search' and '10-Brand', assess new user experience", "embedding": "[-0.045204192,0.004675136,-0.0016771171]", "platform": "openai", "model": "text-embedding-3-large", "cost": 24.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv2Overview", "text_value": "Revenue fell due to fewer orders Heavy hits in 'Paid Search' caused it", "embedding": "[-0.022692261,-0.034839414,0.013242222]", "platform": "openai", "model": "text-embedding-3-large", "cost": 15.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv2KeyInsights", "text_value": "Orders: -$39 vs. Revenue: -$13 '10-Brand' campaign critically affected Revenue", "embedding": "[-0.009858852,0.015905013,-0.009943454]", "platform": "openai", "model": "text-embedding-3-large", "cost": 19.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv2VisualSummary", "text_value": "'Orders' is the top contributing funnel step", "embedding": "[-0.033910457,-0.008339298,-0.012056539]", "platform": "openai", "model": "text-embedding-3-large", "cost": 9.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv3IncidentDetails", "text_value": "The Revenue for the product category 'Outlet Bottoms' was reduced, diverging from the baseline by -100%", "embedding": "[-0.01261549,-0.008175135,-0.009246762]", "platform": "openai", "model": "text-embedding-3-large", "cost": 23.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv3RootCauseAnalysisSummary", "text_value": "Decrease mostly driven by significant drop in Orders", "embedding": "[-0.011643474,-0.043556053,-0.0072968774]", "platform": "openai", "model": "text-embedding-3-large", "cost": 9.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 1, "revenue_ticket_field": "lv3RootCauseAnalysisDetails", "text_value": "'Paid Search' and new users majorly contributed to the Revenue decline. '10-Brand' campaign and product-specific 'White Dino XL Skirt' showed critical impacts", "embedding": "[-0.03687408,-0.0494117,-0.014840893]", "platform": "openai", "model": "text-embedding-3-large", "cost": 35.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "category", "text_value": "Bottoms", "embedding": "[-0.023158956,0.0025241678,-0.012179392]", "platform": "openai", "model": "text-embedding-3-large", "cost": 2.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv1ExecutiveSummary", "text_value": "Orders decrease impacted Revenue Review and correct website issues that lead to quick navigation away", "embedding": "[-0.031512782,0.04167434,-0.0074836877]", "platform": "openai", "model": "text-embedding-3-large", "cost": 15.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv2Overview", "text_value": "Revenue declined primarily due to fewer orders Specific products influenced top Revenue decline", "embedding": "[-0.008540593,-0.0026317544,-0.0073094233]", "platform": "openai", "model": "text-embedding-3-large", "cost": 13.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv2KeyInsights", "text_value": "Orders: -$961 vs Revenue: -$741 Issues at checkout influencing decline", "embedding": "[-0.030331915,0.059543744,-0.004743562]", "platform": "openai", "model": "text-embedding-3-large", "cost": 14.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv2VisualSummary", "text_value": "Orders is the top contributing funnel step", "embedding": "[-0.03186317,-0.0037467452,-0.014019336]", "platform": "openai", "model": "text-embedding-3-large", "cost": 7.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv3IncidentDetails", "text_value": "The revenue for the \"Bottoms\" category decreased by $741 last period, deviating by -20% from the baseline", "embedding": "[-0.009788696,-0.017853124,-0.0045034424]", "platform": "openai", "model": "text-embedding-3-large", "cost": 25.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv3RootCauseAnalysisSummary", "text_value": "The main negative impact came from orders, while checkouts improved Revenue", "embedding": "[-0.011117554,0.02590254,-0.0068580955]", "platform": "openai", "model": "text-embedding-3-large", "cost": 13.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}, {"revenue_ticket_id": 2, "revenue_ticket_field": "lv3RootCauseAnalysisDetails", "text_value": "Specific products like 'Prowess Is Power Straight Leg Pants - Pink / 2X' and 'Humming Along Embroidered Overalls - Denim / 16 (AU)' were key contributors. Analysis suggested focusing on product-specific factors such as pricing or promotional strategies", "embedding": "[-0.044124223,-0.026516402,-0.0336107]", "platform": "openai", "model": "text-embedding-3-large", "cost": 55.0, "cost_unit": "token", "model_params": "{\"dimensions\":1024}"}]}