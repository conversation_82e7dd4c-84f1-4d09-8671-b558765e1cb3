import fs from 'fs';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function seed(client) {
  const dataPath = __dirname + '/data';
  const files = fs.readdirSync(dataPath);

  for (const file of files) {
    const data = JSON.parse(fs.readFileSync(`${dataPath}/${file}`, 'utf8'));
    const tables = Object.keys(data);

    for (const table of tables) {
      const rows = data[table];

      for (const row of rows) {
        const columns = Object.keys(row);
        const values = columns.map(column => row[column]);

        const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${values
          .map((_, i) => `$${i + 1}`)
          .join(', ')})`;

        await client.query(query, values);
      }
    }
  }

  return true;
}

export default seed;