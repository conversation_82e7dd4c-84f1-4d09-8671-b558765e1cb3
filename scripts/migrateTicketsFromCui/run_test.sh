#!/bin/bash

# Check if ENV is provided
if [ -z "$1" ]; then
    echo "Error: ENV parameter is required"
    exit 1
fi

# Check if ORG_ID is provided
if [ -z "$2" ]; then
    echo "Error: ORG_ID parameter is required"
    exit 1
fi

ENV=$1
ORG_ID=$2
STARTING_PAGE=${3:-1}

echo "Running migration for env $ENV, CUI account $ORG_ID, starting from page $STARTING_PAGE"

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/${TIMESTAMP}_${ENV}_${ORG_ID}_page${STARTING_PAGE}.log"

node index.js $ENV $ORG_ID $STARTING_PAGE > $LOG_FILE
