# Migration scripts

Migration deletes existing tickets from the Insights App BE Database, the `insights` table, and repopulates it fetching everything from the CUI.

Make sure to take a backup of the `insights` table before doing anything. Use DBeaver to export the table in JSON format, store that JSON file in `dumps/${ENV}/whatever_filename.json`, then add that file name as a value of the constant `DB_DUMP_FILE_NAME` in the `constants.json` file. This will be used as a backup

For extra safety, all the pages fetched from the CUI will be stored in a `backup` folder.

## How to migrate an org

1. Change the `DB_HOST` environment variable to the environment you need to run the migration
2. Add a fresh access token to the `tokens/${ENV}.json` file
3. Connect to the VPN
4. Run `./run_test.sh test 644` to migrate the tickets for CUI account 644 from the CUI to the Insights App in the test environment. Adjust arguments as needed.
5. Open the latest file in the `logs` folder to track the progress.

## How to resume a migration that was disrupted

1. Check the logs to identify the page for which the error occured.
2. After you have identified and fixed the issue, resume the `./run_test.sh test 644 19` (last argument is the starting page, defaults to 1)
