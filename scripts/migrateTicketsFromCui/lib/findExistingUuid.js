const { kysely } = require('../../../src/common/utils/pgConn');
const logger = require('../../../src/common/utils/logger').default;
const { DB_DUMP_FILE_NAME } = require('../constants.json');

const fs = require('fs');
const path = require('path');

const findExistingOrGenerateUuid = async (env, executionId) => {
  const dumpData = JSON.parse(
    fs.readFileSync(path.join(__dirname, '..', 'dumps', env, DB_DUMP_FILE_NAME)),
  );

  const result = await kysely
    .selectFrom('insights')
    .select('id')
    .where('executionId', '=', executionId)
    .executeTakeFirst();

  const existingUuidInDb = result?.id;
  const existingUuidInDump = dumpData.find(x => x.execution_id === executionId)?.id;
  const uuid = existingUuidInDb ?? existingUuidInDump ?? crypto.randomUUID();

  if (existingUuidInDb) logger.warn(`Uuid found in DB for execution ${executionId}`);
  else if (existingUuidInDump) logger.warn(`Uuid found in DB dump for execution ${executionId}`);
  else logger.warn(`Generated new UUID for execution ${executionId}`);

  return uuid;
};

module.exports = findExistingOrGenerateUuid;
