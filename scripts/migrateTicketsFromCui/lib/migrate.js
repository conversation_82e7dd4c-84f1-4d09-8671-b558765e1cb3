const axios = require('axios');
const transformCuiToInsight = require('./transformCuiToInsight');
const { validateAndPrepareInsight } = require('../../../src/common/services/insightsService');
const fs = require('fs');
const { kysely } = require('../../../src/common/utils/pgConn');
const { MIN_DATE } = require('../constants.json');

const getBatch = async (env, orgId, page) => {
  console.log('Retrieving batch', orgId, page);

  const token = require(`../tokens/${env}.json`).value;
  const getUrl = require(`../APIs/${env}`);

  const url = getUrl(orgId, page);

  const {
    data: { incidents: insights },
  } = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  console.log('Retrieved batch', orgId, page);
  console.log('Latest date', insights[0]?.date);
  return insights;
};

const storeRawBatch = async (env, orgId, page, batch) => {
  console.log('Backing up batch', orgId, page);
  const jsonString = JSON.stringify(batch, null, 4);
  try {
    await fs.promises.mkdir(`backup/${env}/${orgId}`, { recursive: true });
  } catch (e) {
    void e;
  }
  await fs.promises.writeFile(`backup/${env}/${orgId}/page-${page}.json`, jsonString);
  console.log('Backed up batch', orgId, page);
};

const deleteExistingInsights = async batch => {
  const executionIds = batch.map(x => x.metadata?.executionId ?? 'ignore-this-non-existent-id');
  console.log(
    `Deleting ${batch.length} insights with display IDs:`,
    batch.map(x => x.id),
  );
  const deletedRows = await kysely
    .deleteFrom('insights')
    .where('executionId', 'in', executionIds)
    .returning('displayId')
    .execute();
  console.log(
    `Deleted ${deletedRows.length} insights with display IDs:`,
    deletedRows.map(row => row.displayId),
  );
};

const convertFormat = async (env, batch) => {
  const results = await Promise.allSettled(batch.map(x => transformCuiToInsight(env, x)));
  const converted = results
    .filter(result => {
      if (result.status === 'rejected') {
        console.error('Failed to transform insight:', result.reason);
        return false;
      }
      return true;
    })
    .map(result => result.value);
  return converted;
};

const storeToDB = async preparedBatch => {
  console.log('Storing batch to DB');
  await kysely.insertInto('insights').values(preparedBatch).execute();
  console.log('Stored batch to DB');
};

const migrateOnePage = async (env, orgId, page) => {
  const batch = await getBatch(env, orgId, page);
  await storeRawBatch(env, orgId, page, batch);
  await deleteExistingInsights(batch);
  const convertedBatch = await convertFormat(env, batch);
  const preparedBatch = convertedBatch.map(validateAndPrepareInsight).filter(x => x);
  await storeToDB(preparedBatch);
  return preparedBatch;
};

const migrateOneOrganization = async (env, orgId, startingPage = 1) => {
  let page = startingPage;
  let hasMore = true;

  while (hasMore) {
    try {
      console.log('Processing page', page, 'for org', orgId);
      const batch = await migrateOnePage(env, orgId, page);

      const noMoreTickets = batch.length === 0;
      const reachedDateLimit = new Date(batch[batch.length - 1].date) < new Date(MIN_DATE);

      if (noMoreTickets || reachedDateLimit) {
        console.log(
          `No more tickets because ${noMoreTickets ? 'no more tickets' : 'reached date limit'}.`,
        );
        hasMore = false;
      } else {
        page++;
      }
    } catch (error) {
      console.error('Error processing page', page, 'for org', orgId, error);
      hasMore = false;
    }
  }

  console.log('Completed migration for org', orgId);
  return true;
};

module.exports = { migrateOnePage, migrateOneOrganization };
