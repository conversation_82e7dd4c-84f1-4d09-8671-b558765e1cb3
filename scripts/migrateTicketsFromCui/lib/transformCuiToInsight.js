const findExistingOrGenerateUuid = require('./findExistingUuid');

async function transformToSchema(env, inputJson) {
  const impactsMap = {
    itemsviewed: inputJson.metadata.productViewsRevenueImpact,
    itemsaddedtocart: inputJson.metadata.cartsRevenueImpact,
    itemscheckedout: inputJson.metadata.checkoutsRevenueImpact,
    itemspurchased: inputJson.metadata.ordersRevenueImpact,
    'itemrevenue/itemavgvalue': inputJson.metadata.aovRevenueImpact,
    itemrevenue: 0,
  };

  const getCurrency = symbol => (symbol === '$' ? 'USD' : 'EUR');

  // Create base object with required fields
  const transformed = {
    id: await findExistingOrGenerateUuid(env, inputJson.metadata.executionId),
    displayId: inputJson.id,
    cuiId: inputJson.id,
    visible: inputJson.visible,
    incidentTimeStamp: inputJson.date,
    title: inputJson.title,
    executionId: inputJson.metadata.executionId,
    aisExecutionId: inputJson.metadata.aisExecutionId ?? crypto.randomUUID(),
    orgId: inputJson.auth0OrgID,
    granularity: 'daily', // Default from construct_sns_event_ticket_data.py

    // Arrays from metadata
    executiveSummary: inputJson.metadata.lv1ExecutiveSummary,
    overview: inputJson.metadata.lv2Overview,
    keyInsights: inputJson.metadata.lv2KeyInsights,
    actions: inputJson.metadata.lv2Actions,
    visualSummary: inputJson.metadata.lv2VisualSummary,
    incidentDetails: inputJson.metadata.lv3IncidentDetails,
    rootCauseAnalysisSummary: inputJson.metadata.lv3RootCauseAnalysisSummary,
    rootCauseAnalysisDetails:
      typeof inputJson.metadata.lv3RootCauseAnalysisDetails === 'string'
        ? inputJson.metadata.lv3RootCauseAnalysisDetails
        : inputJson.metadata.lv3RootCauseAnalysisDetails.join(' '),

    // Values from metadata
    revenueExpectedDeviationPct: inputJson.metadata.revenueExpectedDeviationPct,
    baseline: inputJson.metadata.baseline,
    revenueDelta: inputJson.metadata.revenueDelta,
    anomalyDetectionMode: inputJson.metadata.anomalyDetectionMode || 'TSA',
    sensitivity: inputJson.sensitivity,

    // Transform dimensions to breakdown array
    breakdown: inputJson.dimensions.map(dim => ({
      key: dim.name.replace(' ', '_'),
      value: dim.value,
    })),

    hypotheses: inputJson.metadata.hypotheses,

    // Transform funnel metrics from metadata.metrics
    funnelMetrics: inputJson.metadata.metrics.map(metric => ({
      id: metric.id,
      name: metric.name,
      baselineValue: metric.baselineValue,
      actualValue: metric.actualValue,
      keyMetricImpact: impactsMap[metric.id],
      ...(metric.derived && { derived: true }),
    })),

    // Transform key metric
    keyMetric: {
      id: 'itemrevenue',
      name: 'Products revenue',
      type: 'currency',
      currency: getCurrency(inputJson.keyMetric.symbol),
      symbol: inputJson.keyMetric.symbol,
      value: parseFloat(inputJson.keyMetric.value),
      last_value: parseFloat(inputJson.keyMetric.last_value),
      last_year_value: 0, // This needs to be provided
      expected_range: {
        min: parseFloat(inputJson.keyMetric.expected_range_min),
        max: parseFloat(inputJson.keyMetric.expected_range_max),
      },
      time_series: inputJson.incidentGraph.time_series.map(point => ({
        metric: point.metric,
        period: point.period,
      })),
    },
    aiActions: inputJson.metadata.actions,
    aiSuggestions: inputJson.metadata.lv1ExecutiveSummary[2] ?? '',
  };

  return transformed;
}

module.exports = transformToSchema;
