require('dotenv').config();
const {
  // migrateOnePage,
  migrateOneOrganization,
} = require('./lib/migrate');

const ENV = process.argv[2];
const ORG_ID = process.argv[3];
const STARTING_PAGE = (process.argv[4] && parseInt(process.argv[4])) || 1;

if (!ENV || !ORG_ID) {
  console.error('Usage: node index.js <ENV> <ORG_ID> [STARTING_PAGE]');
  process.exit(1);
}

(async function main() {
  //await migrateOnePage('644', 1);
  await migrateOneOrganization(ENV, ORG_ID, STARTING_PAGE);
})();
