diff --git a/src/common/services/insightsService.js b/src/common/services/insightsService.js
index 13a7d07..c7605a8 100644
--- a/src/common/services/insightsService.js
+++ b/src/common/services/insightsService.js
@@ -36,6 +36,7 @@ function validateAndPrepareInsight(insight) {
   if (!isValid) {
     const errorMessage = `Insight failed to pass validation: ${ajv.errorsText(validate.errors)}`;
     logger.error(errorMessage, { insight, errors: validate.errors });
+    return null;
     throw new Error(errorMessage);
   }

