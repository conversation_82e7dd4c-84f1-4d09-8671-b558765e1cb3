# Steps to run

First extract all data to the appropriate folders, see readme files in each folder.

## TEST

1. Add the TEST DB details to an .env file.
2. Update the environment to `test` inside the `prepare.js` file.
3. Run `node index.js > output_test.json`.
4. Update the environment to `test` inside the `import.js` file.
5. Run `node import.js`.

## PROD

1. Add the PROD DB details to an .env file.
2. Update the environment to `prod` inside the `prepare.js` file.
3. Run `node index.js > output_prod.json`.
4. Update the environment to `prod` inside the `import.js` file.
5. Run `node import.js`.
