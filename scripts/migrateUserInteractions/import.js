require('dotenv').config();

const { kysely } = require('../../src/common/utils/pgConn');
const BATCH_SIZE = 100;

async function main(env) {
  const data = require(`./output_${env}.json`);

  let batch;
  do {
    batch = data.splice(0, BATCH_SIZE);
    const rows = await kysely
      .insertInto('user_interactions')
      .values(batch)
      .returningAll()
      .execute();
    console.log(`Inserted ${rows.length} rows. ${data.length} rows left.`);
  } while (batch.length > 0);
}

main('test');
