const loadResources = env => {
  const insights = require(`./insights/${env}.json`);
  const orgs = require(`./orgs/${env}.json`);
  const users = require(`./users/${env}.json`);
  const userInteractions = require(`./userInteractions/${env}.json`);

  return { insights, orgs, users, userInteractions };
};

const findAuth0Id = (orgs, cuiId) => orgs.find(x => x.cuiId === cuiId)?.auth0Id;

const getAction = (gsheetRecord, insights) => {
  const isAiSuggestion =
    gsheetRecord['Action value'] === 'Like' || gsheetRecord['Action value'] === 'Dislike';
  const isHypothesisClick = gsheetRecord.Action === 'Hypotheses tested click';
  const isFeedback = gsheetRecord.Action === 'Hypotheses tested feedback submission';

  const insight = insights.find(x => x.display_id == gsheetRecord['Ticket ID']);
  if (!insight) {
    console.warn(`Insight ${gsheetRecord['Ticket ID']} not found`);
    return;
  }

  let action;

  switch (true) {
    case isAiSuggestion: {
      const aiActions = JSON.parse(insight.ai_actions);
      const immediateAction = aiActions.immediate?.find(x => x.action === gsheetRecord.Action);
      const longtermAction = aiActions.long_term?.find(x => x.action === gsheetRecord.Action);

      action = {
        action_type: 'ai-suggestions-click',
        action_target_id: immediateAction?.id || longtermAction?.id,
        action_target_group: immediateAction ? 'immediate' : 'long_term',
        action_value: gsheetRecord['Action value'],
      };
      break;
    }
    case isHypothesisClick: {
      const hypotheses = JSON.parse(insight.hypotheses);
      const hypothesis = hypotheses.find(x => x.statement == gsheetRecord['Action value']);
      if (!hypothesis) break;

      action = {
        action_type: 'hypothesis-click',
        action_target_id: hypothesis.id,
        action_value: gsheetRecord['Action value'],
      };
      break;
    }
    case isFeedback: {
      action = {
        action_type: 'hypothesis-feedback-submission',
        action_value: gsheetRecord['Action value'],
      };
      break;
    }
    default:
      console.warn(`Unknown action type: ${gsheetRecord.Action}`);
  }

  return action;
};

const findUserId = (gsheetRecord, users) => {
  return users.find(
    x => x.email === gsheetRecord['User email address'].replace(/^([^+]+)\+[^@]+(@[^@]+)$/, '$1$2'),
  )?.user_id;
};

const mapData = (resources, gsheetRecord) => {
  const action = getAction(gsheetRecord, resources.insights);
  if (!action) return;

  return {
    timestamp: `${gsheetRecord['Date (UTC)']}T${gsheetRecord['Time (UTC)']}Z`,
    user_id: findUserId(gsheetRecord, resources.users),
    org_id: findAuth0Id(resources.orgs, gsheetRecord['Organization ID']),
    insight_id: gsheetRecord['Ticket ID'],
    ...action,
  };
};

const isValidRecord = r => {
  return r.timestamp && r.user_id && r.org_id && r.insight_id && r.action_type && r.action_value;
};

const main = async env => {
  const resources = loadResources(env);
  const res = resources.userInteractions
    .map(x => mapData(resources, x))
    .filter(x => x && isValidRecord(x));
  console.log(JSON.stringify(res, null, 2));
};

main('test');
