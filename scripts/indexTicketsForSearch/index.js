const processSqsNotifs = require('../../src/sqs/services/ticketService.js');
const fetchAPage = require('./fetch.js');
const orgs = require('./orgIds.js');

const tokens = {
  TEST: require('./TEST/token'),
  PROD: require('./PROD/token'),
};

const urls = {
  TEST: require('./urls.js').testUrl,
  PROD: require('./urls.js').prodUrl,
};

const ENV = 'PROD';

const main = async () => {
  const token = tokens[ENV];
  const org = 1;
  const { orgId, auth0OrgID } = orgs[ENV][org];

  let page = 1;

  do {
    console.log(`\n\nFetching page ${page}...`);
    const url = urls[ENV](orgId, page);
    console.log('url', url);
    console.log('token', '...' + token.slice(-8));
    console.log('orgId', orgId);
    const { incidents } = await fetchAPage(url, token);
    console.log(`Page ${page} fetched nof incidents ${incidents.length}`);
    if (incidents.length === 0) {
      break;
    }

    await processSqsNotifs(
      incidents.map(incident => ({
        'event-type': 'ticket-created',
        message: { ...incident, auth0OrgID: incident.auth0OrgID || auth0OrgID },
      })),
    );
    page++;
  } while (true); // eslint-disable-line no-constant-condition
};

main();
