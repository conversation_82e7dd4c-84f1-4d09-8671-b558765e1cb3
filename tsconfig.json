{"compilerOptions": {"target": "es2022", "module": "NodeNext", "moduleResolution": "NodeNext", "moduleDetection": "force", "rootDir": "./", "baseUrl": "./", "outDir": "./dist", "lib": ["es2022"], "allowJs": true, "esModuleInterop": true, "types": ["node", "jest"], "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.js", "src/**/*.ts", "test/**/*.ts", "**/*.test.ts"], "exclude": ["node_modules", "dist"], "ts-node": {"transpileOnly": true, "files": true}}