FROM node:20.11-slim AS build_stage

WORKDIR /app

# Only copy the files needed to install dependencies; this facilitates docker layer caching
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:20-slim AS local_build

WORKDIR /app

# Only copy the files needed to install dependencies; this facilitates docker layer caching
COPY package*.json ./
RUN npm ci

# Needed for linting and tests
COPY . .

COPY --from=build_stage ./app/dist ./dist

# Start npm
CMD ["npm", "run", "dev"]

FROM public.ecr.aws/lambda/nodejs:20 AS lambda_build

WORKDIR ${LAMBDA_TASK_ROOT}

# Only copy the files needed to install dependencies; this facilitates docker layer caching
COPY package*.json ./
RUN npm ci --omit=dev
COPY --from=build_stage ./app/dist ./dist

# Needed for e.g. contracts folder
COPY . .

# Set the CMD to your handler (could also be done as a parameter override outside of the Dockerfile)
CMD [ "./dist/src/lambda.handler" ]
