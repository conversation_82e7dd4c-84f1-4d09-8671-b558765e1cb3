# insights-app-be

This is a Node.js app acting as the backend for the Insights App BackEnd.

It is deployed as a Docker container Lambda and is accessible via Cloudfront [at these URLs](https://bare-square.atlassian.net/wiki/spaces/ENG/pages/172654599/Domain+names+of+various+services#Insights-App-BE).

## Prerequisites

Before you can run this app, make sure you have the following installed on your machine:

- Node.js (version 20.11.0 or higher)
- npm (Node Package Manager)

## Getting Started

Follow these steps to start the app:

1. Clone the repository to your local machine:

   ```bash
   git clone --recurse-submodules https://github.com/your-username/insights-app-be.git
   ```

   Pay attention to the `--resurse-submodules` parameter. Since this project includes submodules, you have to use this param to fetch them locally.

   If you already have cloned the project locally without the submodules, and you now need to fetch them, use:

   ```
   git submodule update --init --recursive
   ```

2. Navigate to the project directory:

   ```bash
   cd insights-app-be
   ```

3. Install [Node Version Manager](https://github.com/nvm-sh/nvm?tab=readme-ov-file#install--update-script) (if you don't have it installed):

   ```bash
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
   ```

4. `nvm install` and then `nvm use`. It will install and use the node version mentioned in the `.nvmrc` file.

5. Install the dependencies:

   ```bash
   npm install
   ```

6. Copy the `.env.template` root file into `.env` and fill in the missing values.

### Run without Docker Compose

1. Ensure you have a Redis database running; it could be a local installation, or you can start one as follows:
   - edit `.env` to contain line `REDIS_URL=redis://localhost:6379/3`
   - issue command ([details](https://docs.docker.com/network/network-tutorial-host/)):
     ```commandline
     docker run --rm -d --network host --name redis redis:7-alpine
     ```
2. Ensure you have a PostgreSQL database running, either as a local installation or using a similar command as above
3. Start the app with `npm run dev`; the server will be available at `http://localhost:3003` and _supports live reload_
4. Linting/testing with `npm run lint` and `npm run test`, respectively.

### Run with Docker Compose

#### For development

To avoid having a local setup for PostgreSQL and Redis, you can run the app with Docker Compose.

1. Ensure your .env has the following lines:
   ```shell
   DOCKER_BUILD=local_build
   DOCKER_PORT=3003
   REDIS_URL=redis://redis:6379/3
   ```
2. Ensure your .env does **not** have a `REDIS_PASSWORD` variable.
3. Run the `~/aws_cli_mfa.py` script; this will allow retrieving values from SSM
4. Run `docker compose up`; this starts the app in a container, as well as:
   - a PostgreSQL database container with the `pgvector` extension and an empty database named `insights`.
   - a Redis container
5. The server will be available at `http://localhost:3003` and _supports live reload_
6. Linting/testing:
   ```shell
   docker compose build linting
   docker compose up linting
   docker compose build tests
   docker compose up tests
   ```

#### For checking the build

In some cases it is useful to run the app with Docker Compose as above, but using the same image deployed in the Lambda.
This can help e.g. with build issues, but requires quirky syntax to invoke the app. Note that this setup does not
support live reload.

1. Ensure your .env has the following lines:
   ```shell
   DOCKER_BUILD=lambda_build
   DOCKER_PORT=8080
   REDIS_URL=redis://redis:6379/3
   ```
2. Run the `~/aws_cli_mfa.py` script; this will allow retrieving values from SSM
3. Run `docker compose up insights-app-be`; this starts the app (no support for live reload), as well as:
   - a PostgreSQL database container with the `pgvector` extension and an empty database named `insights`.
   - a Redis container
4. Access the app like so:
   ```shell
   $ curl -XPOST "http://localhost:3003/2015-03-31/functions/function/invocations" -d '{"path": "/status"}'
   Invalid token
   ```

#### Using the TEST environment databases

1. Connect to the AWS VPN
2. Modify the PostgreSQL-related environment variables in your `.env` as follows:
   ```
   DB_HOST=diagnostics-buffy.us-east-1.test.baresquare.com
   DB_NAME=insights
   DB_USERNAME=<your RDS username>
   DB_PASSWORD=<your RDS password>
   ```
3. Modify the Redis-related environment variable as follows:
   ```
   REDIS_URL=rediss://<EMAIL>:6379/3
   ```
   Set the value of the `REDIS_PASSWORD` environment variable to the value of the SSM key `/insights_app_be/elasticache_redis/password`.
4. Run `docker compose up insights-app-be`; this starts only the app as a service, without the Redis/PostgreSQL
   containers

## Working with submodules

Read [more](./docs/submodules.md).

## Migrations

Useful commands to run the migrations locally from CLI:

- `npm run migrate create create xyz table`: Creates a new file in the migrations folder called: 1712919692344_create-xyz-table.ts
- `DATABASE_URL=postgres://username:password@localhost:5432/insights npm run migrate up`: Runs the pending migrations. DATABASE_URL is required and could be omitted if we add this to the env file. Make sure to replace username and password based on the local setup.
- `DATABASE_URL=postgres://username:password@localhost:5432/insights npm run migrate down`: Rollback the latest migration

## Logging

The application uses the `winston` library for logging. Logs are configured to redact sensitive information using a custom redact function. In local development, logs are formatted with timestamps and printed in a readable format. In cloud environments, logs are formatted as JSON. You can find the logger configuration in the `src/common/utils/logger.js` file. To see sensitive data locally, you can comment out the line that applies the redact format, but make sure not to commit this change.

### How to use the logger

To use the logger, you can follow these examples:

1. Logging a simple string:

   ```javascript
   logger.info('This is a simple log message');
   // cloud:
   // => {'level': 'info', 'message': 'This is a simple log message'}
   // locally:
   // => 2024-05-08 12:00:00.123 - INFO: This is a simple log message
   ```

2. Logging variables:

   ```javascript
   const user = { id: 1, name: 'John Doe' };

   logger.info('User details', user);
   // => {'level': 'info', 'message': 'User details:', 'metadata': {'user': {'id': 1, 'name': 'John Doe'}}}

   const query = 'SELECT * FROM table';
   // use this format to log multiple variables
   logger.info('User details', { user, query });
   // => {'level': 'info', 'message': 'User details:', 'metadata': { 'user': {'id': 1, 'name': 'John Doe'}, 'query': 'SELECT * FROM table' }}
   ```

3. Logging an object without a message:
   ```javascript
   const errorDetails = { code: 500, message: 'Internal Server Error' };
   // the object will be placed inside the 'message' property. avoid this if possible
   logger.info(errorDetails);
   // => {'level': 'error', 'message': { code: 500, message: 'Internal Server Error' }}
   ```

### How to use levels

```javascript
logger.debug('This line will not appear in datadog`s regular flow, but is visible in its live trail');
logger.info('This is an info log');
logger.warn('This is a warn log');
logger.error('This is an error log');
logger.debug('This is a debug log');
```

### Redaction

A set of values can be defined to be redacted from the logs. This is useful to avoid leaking sensitive information. The keys whose values are to be redacted are defined in the `src/common/utils/logger/sensitiveKeys.js` file in regex format. These keys will be redacted wherever they are found inside an object passed to the logger.

```javascript
const obj = {
  id: 1,
  name: 'John Doe',
  password: '123456',
  request: {
    headers: {
      Authorization: 'Bearer 123456',
    },
  },
};
logger.info('User details:', { obj });
// => {'level': 'info', 'message': 'User details:', obj: {'id': 1, 'name': 'John Doe', 'password': '[REDACTED]', 'request': {'headers': {'Authorization': '[REDACTED]'}}}}
```

## Code Organization

[![C4 Components](./docs/assets/C4_Components.png)](./docs/assets/C4_Components.png)
_C4 Components Diagram_

### Sub-systems

The code is organized inside the `src` folder into two sub-systems, one for each external data source.

- The `src/http` folder hosts the Express API app.

  The API provides access to external services to data and functionalities of the Insights App BE.

- The `src/sqs` folder hosts the Event Consumer.

  The Event Consumer is subscribed to the `airflow_ticket_submission` SNS topic, via the `insights_app_be` SQS queue. The Airflow SNS topic emits new Insights that will be stored directly into the Insights App's database, and will also be indexed for the search functionality.

### Entry points

- Each of the sub-systems exposes an `index.js` file as its entry point.
- A third folder, `src/common` hosts services and utility functions that are common to both sub-systems.
- The `src/index.js` file serves as a router that decides which sub-system must be invoked, based on the AWS Lambda invocation event schema.
- The `lambda.mjs` file wraps the whole system in the Lambda invocation handler, along with the initialization code that is utilized in the Lambda provisioning phase.
- The `server.js` file is used for local development and only loads the Express app.

## Search

For detailed information about the search algorithm, please refer to the [Search function walk-through](./docs/search.md) document.

The experimental Search v3 documentation can be found [here](./docs/search_v3.md).

## Test Strategy

For detailed information about our testing approach, please refer to the [Test Strategy](./docs/test-strategy.md) document.
