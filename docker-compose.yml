version: '3.4'
services:
  insights-app-be:
    env_file: .env
    restart: always
    container_name: ia-be
    build:
      context: .
      target: ${DOCKER_BUILD}
      dockerfile: ./docker/Dockerfile
    depends_on:
      insights-app-be-db:
        condition: service_started
    volumes:
      - ~/.aws:/root/.aws:ro
      - ./src:/app/src
    ports:
      - '${PORT}:${DOCKER_PORT}'
    environment:
      - PORT=${DOCKER_PORT}
      - INSIGHTS_BFF_URL=${INSIGHTS_BFF_URL}
      - BE_APP_AUTH0_AUDIENCE=${BE_APP_AUTH0_AUDIENCE}
      - FLOW_API_AUTH0_AUDIENCE=${FLOW_API_AUTH0_AUDIENCE}
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - DB_HOST=${DB_HOST}
      - DB_NAME=${DB_NAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - LOGGER_LEVEL=${LOGGER_LEVEL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_URL=${REDIS_URL}
      - PL_ENV=development
      - AWS_PROFILE=bsq-test-us

  insights-app-be-db:
    restart: always
    container_name: ia-be-db
    image: postgres:14.10
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: insights
    ports:
      - '${DB_HOST_PORT}:${DB_PORT}'
    volumes:
      - db_volume:/var/lib/postgresql/data
      - ./docker/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    command: >
      sh -c "apt-get update &&
             apt-get install -y postgresql-14-pgvector &&
             docker-entrypoint.sh postgres"
    deploy:
      replicas: 1

  redis:
    restart: always
    container_name: ia-redis
    image: redis:7-alpine
    ports:
      - '6379:6379'
    command: redis-server --save 20 1 --loglevel warning
    volumes:
      - redis_volume:/data

  base-local:
    build:
      context: .
      target: local_build
      dockerfile: ./docker/Dockerfile
    environment:
      - PL_ENV=dev
      - PORT=${PORT}

  tests:
    extends:
      service: base-local
    container_name: ia-be-tests
    command: ['npm', 'run', 'test']
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}

  linting:
    extends:
      service: base-local
    container_name: ia-be-linting
    command: ['npm', 'run', 'lint']

volumes:
  db_volume:
    name: 'insights-app-db'
  redis_volume:
    name: 'insights-app-redis'
