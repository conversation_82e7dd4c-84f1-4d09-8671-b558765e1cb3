schema-version: v2.2
dd-service: insights_app_be
team: insights-app
application: insights-app
description: "The backend (BE) for service Insights App FE"
tier: "1"
lifecycle: "production"
type: web
languages:
  - js
contacts:
  - type: slack
    contact: https://baresquare.slack.com/archives/C066VFB9WSZ
links:
  - name: insights-app-be
    type: repo
    provider: github
    url: https://github.com/BareSquare/insights-app-be
  - name: Insights App Confluence Space
    type: doc
    provider: Confluence
    url: https://bare-square.atlassian.net/wiki/spaces/TIA/overview
  - name: Insights App JIRA project
    type: other
    url: https://bare-square.atlassian.net/jira/software/c/projects/INSIGHTS/boards/31
    provider: Jira
  - name: Insights App Miro board
    type: other
    url: https://miro.com/app/board/uXjVNLrEhKU=/
    provider: Miro