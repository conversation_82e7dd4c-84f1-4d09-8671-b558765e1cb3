import forecastSchema from './revenue-plan-forecasting/contracts/v1/output_schema.json' with { type: 'json' };
import forecastExample from './revenue-plan-forecasting/contracts/v1/forecast_data_example.json' with { type: 'json' };

import {
  addIdentifier,
  convertPropertiesToCamelCase,
  updateRefs,
} from '../src/common/utils/jsonSchema.js';

const deepCopy = (obj: unknown) => JSON.parse(JSON.stringify(obj));

const forecastSchemaApi = deepCopy(forecastSchema);
convertPropertiesToCamelCase(forecastSchemaApi);
addIdentifier(forecastSchemaApi, 'id', {
  title: 'Serial number',
  type: 'number',
});

const forecastSchemaApiDocs = deepCopy(forecastSchemaApi);
updateRefs(forecastSchemaApiDocs);

export { forecastSchema, forecastSchemaApi, forecastSchemaApiDocs, forecastExample };
