import io
import os
import tomllib

import pandas as pd
from dotenv import load_dotenv

from app.definitions import get_project_root
from app.logger import logger
from app.services.aws import S3Client
from app.services.google_sheet import access_google_sheet, google_sheet_to_pandas, store_dataframe_to_gsheet

ROOT_DIR = get_project_root()

load_dotenv()


def complete_date_range(df: pd.DataFrame) -> pd.DataFrame:
    """Ensure a complete range of dates in the DataFrame by filling in missing dates with zero revenue.

    Args:
        df (pd.DataFrame): DataFrame with 'date' and 'revenue' columns

    Returns:
        pd.DataFrame: DataFrame with complete date range and zero revenue for missing dates
    """
    # Ensure 'date' column is datetime
    df["date"] = pd.to_datetime(df["date"])

    # Set 'date' as index to easily reindex
    df.set_index("date", inplace=True)

    # Create a full date range from min to max date in the data
    min_date = df.index.min()
    max_date = df.index.max()
    full_date_range = pd.date_range(start=min_date, end=max_date)

    # Reindex the DataFrame to include all dates in the range
    df = df.reindex(full_date_range)

    # Fill missing revenue values with 0 and ensure integer type
    df["revenue"] = df["revenue"].fillna(0).astype(int)

    # Reset the index to make 'date' a column again
    df.reset_index(inplace=True)
    df.rename(columns={"index": "date"}, inplace=True)

    return df


def compute_revenue_per_date(df):
    # Convert to datetime and create week/year columns
    df["week"] = pd.to_datetime(df["date"]).dt.isocalendar().week
    df["year"] = pd.to_datetime(df["date"]).dt.isocalendar().year

    # Calculate weekly totals per source
    weekly_totals = df.groupby(["year", "week"])["revenue"].sum().reset_index(name="weekly_revenue")

    # Merge weekly totals back to original dataframe
    df = df.merge(weekly_totals, on=["year", "week"], how="left")

    # Calculate daily contribution as percentage
    df["daily_contribution"] = (df["revenue"] / df["weekly_revenue"] * 100).round(0).astype(int)

    # Drop temporary columns if not needed
    return df.drop(["week", "year", "weekly_revenue"], axis=1)


def preprocess_data(df):
    df.rename(
        columns={
            "itemRevenue": "revenue",
            "date": "date",
            "groundTruth": "ground_truth",
        },
        inplace=True,
    )

    # Convert itemRevenue to numeric
    df["revenue"] = pd.to_numeric(df["revenue"]).round().astype(int)

    # Complete the date range
    return complete_date_range(df)


def prepare_daily_contribution_data(google_sheets, creds):
    logger.info("Preparing daily contribution data", extra={"google_sheets": google_sheets})
    sheet_id = google_sheets.get("sheet_id")
    sheet_name = google_sheets.get("sheet_name")

    # Get raw sheet data
    google_sheet = access_google_sheet(
        sheet_id,
        sheet_name,
        creds,
    )

    logger.info("Preparing data")
    # Convert to pandas DataFrame first
    df = google_sheet_to_pandas(google_sheet)
    df = preprocess_data(df)

    df = compute_revenue_per_date(df)

    df["date"] = df["date"].dt.strftime("%Y-%m-%d")

    logger.info("Data successfully prepared")
    return df


def upload_dataframe_to_s3(df: pd.DataFrame, bucket: str, key: str, s3_client: S3Client) -> bool:
    """Uploads a DataFrame as a CSV to S3.

    Args:
        df (pd.DataFrame): DataFrame to upload.
        bucket (str): S3 bucket name.
        key (str): S3 object key.
        s3_client (S3Client): Instance of S3Client.

    Returns:
        bool: True if upload succeeded, False otherwise.
    """
    try:
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        bytes_buffer = io.BytesIO(csv_buffer.getvalue().encode())
        bytes_buffer.seek(0)
        return s3_client.upload_fileobj(
            fileobj=bytes_buffer,
            bucket=bucket,
            key=key,
        )
    except Exception as e:
        logger.error("Failed to upload DataFrame to S3", extra={"error": e, "key": key})
        return False


def store_data(df, split_date, sheet_id, sheet_name, creds, client):
    logger.info("Storing daily contribution data to Google Sheet")
    try:
        store_dataframe_to_gsheet(df, sheet_id, sheet_name + "-processed", creds)
    except Exception as e:
        logger.error("Failed to store data to Google Sheet", extra={"error": e})

    # split data to train and test
    train_df = df[df["date"] < split_date]
    test_df = df[df["date"] >= split_date]
    logger.info("Storing daily contribution data to S3")
    try:
        file_name = f"{client}/daily-contribution"
        s3_client = S3Client()
        upload_dataframe_to_s3(
            train_df,
            os.environ["STORAGE_BUCKET_NAME"],
            file_name + "-train.csv",
            s3_client,
        )
        upload_dataframe_to_s3(
            test_df,
            os.environ["STORAGE_BUCKET_NAME"],
            file_name + "-test.csv",
            s3_client,
        )
        upload_dataframe_to_s3(
            df,
            os.environ["STORAGE_BUCKET_NAME"],
            file_name + "-all.csv",
            s3_client,
        )
    except Exception as e:
        logger.error("Failed to store data", extra={"error": e})

    logger.info("Data successfully stored")


def perform_etl_per_date():
    try:
        with open(ROOT_DIR / "app" / "configs" / "etl.toml", "rb") as f:
            config = tomllib.load(f)

        creds = {
            "clientId": os.environ["GA4_CLIENT_ID"],
            "clientSecret": os.environ["GA4_CLIENT_SECRET"],
            "refresh_token": os.environ["GA4_REFRESH_TOKEN"],
        }
        data = prepare_daily_contribution_data(config.get("google_sheet"), creds)
        try:
            store_data(
                data,
                config.get("split_date"),
                config.get("google_sheet").get("sheet_id"),
                config.get("google_sheet").get("sheet_name"),
                creds,
                config.get("client"),
            )
        except Exception as e:
            logger.error("Failed to store data", extra={"error": e})
    except Exception as e:
        logger.error("Error preparing daily contribution data", extra={"error": e})
        raise e


if __name__ == "__main__":
    perform_etl_per_date()
