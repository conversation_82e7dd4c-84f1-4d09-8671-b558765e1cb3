import os
import traceback

from dotenv import load_dotenv

from app import exceptions
from app.logger import logger
from app.services import aws


def load_ga4_credentials() -> None:
    """Load Google Analytics 4 credentials from SSM."""
    ga4_credentials = aws.get_ssm_parameter("/revenue-plan-forecasting/ga_secrets", return_json=True)

    os.environ["GA4_CLIENT_ID"] = ga4_credentials["clientId"]
    os.environ["GA4_CLIENT_SECRET"] = ga4_credentials["clientSecret"]
    os.environ["GA4_REFRESH_TOKEN"] = ga4_credentials["refresh_token"]

    """Load the Auth0 secrets for this service from SSM."""
    auth0_application = aws.get_ssm_parameter("/revenue-plan-forecasting/auth0/application", return_json=True)

    os.environ["AUTH0_CLIENT_ID"] = auth0_application["data"]["client_id"]
    os.environ["AUTH0_CLIENT_SECRET"] = auth0_application["data"]["client_secret"]

    """Load the Auth0 secrets related to Insights App BE for this service from SSM."""
    os.environ["AUTH0_DOMAIN"] = aws.get_ssm_parameter(ssm_key="/auth0/domain", return_json=False)
    auth0_api_audiences = aws.get_ssm_parameter(ssm_key="/auth0/api/audiences", return_json=True)
    os.environ["AUTH0_API_AUDIENCE_INSIGHTS_APP"] = auth0_api_audiences["insights-app"]

    """Load the URLs for Insights App BE."""
    os.environ["URL_INSIGHTS_APP_BE"] = f"https://insights-app-be.{os.getenv("PL_ENV")}.baresquare.com"

    verify_environment_variables(
        [
            "GA4_CLIENT_ID",
            "GA4_CLIENT_SECRET",
            "GA4_REFRESH_TOKEN",
        ]
    )


def verify_environment_variables(required_vars: list[str]) -> None:
    """Verify that all required environment variables are set."""
    missing_vars = [var for var in required_vars if os.getenv(var) is None]
    if missing_vars:
        raise AssertionError(f"Missing required environment variables: {', '.join(missing_vars)}")


def set_environmental_variables() -> None:
    """Load environment variables from .env file or AWS SSM."""
    try:
        logger.info("Loading environmental variables")
        load_dotenv()

        if (os.getenv("PL_ENV") != "dev") or (os.getenv("AWS_PROFILE") is not None):
            logger.info("Loading environmental variables from AWS SSM")

            load_ga4_credentials()
    except BaseException as e:
        root_cause = exceptions.ex_cause(e, get_root=True)
        logger.critical(
            {
                "message": str(e),
                "error-type": type(e).__name__,
                "root-cause": root_cause,
                "traceback": traceback.format_exception(e),
            }
        )
        raise RuntimeError(f"Unable to set environment variables: {root_cause}") from e


def get_fastapi_config():
    """Generate configuration parameters for FastAPI application.
    Returns a dictionary with appropriate configuration values.
    """
    return {"version": os.getenv("BUILD_TAG", os.getenv("PL_ENV")), "title": os.getenv("PL_SERVICE", "n/a")}
