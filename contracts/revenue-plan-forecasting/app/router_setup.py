from fastapi import APIRouter, Depends, Security
from starlette.requests import Request

from app.logger import logger
from app.services.authentication import VerifyToken


def public_route():
    """Mark routes as public with decorator (no auth required)."""
    def decorator(func):
        setattr(func, "is_public", True)
        return func
    return decorator


async def log_authenticated_request(request: Request):
    """Log details about authenticated requests including user info from JWT payload.
    This function must be called after the auth dependency in the chain.
    """
    # Get auth payload from request state (populated by verify method)
    auth_payload = getattr(request.state, "auth_payload", {})

    # Extract path, method, query params
    path = request.url.path
    method = request.method
    query_params = dict(request.query_params)

    # Extract user information from the auth payload
    user_id = auth_payload.get("sub") if auth_payload else None
    user_email = auth_payload.get("email") if auth_payload else None

    # Log the request
    logger.info(
        path,
        extra={
            "http": {
                "method": method,
                "path": path,
                "query": query_params,
            },
            "user": {
                "id": user_id,
                "email": user_email,
            },
        },
    )

    # Return the original payload in case it's needed downstream
    return auth_payload


def setup_router_dependencies(router: APIRouter):
    """Set up all dependencies for the router including auth and logging."""
    auth = VerifyToken()
    auth_dependency = auth.verify

    log_dependency = log_authenticated_request

    for route in router.routes:
        # Skip routes that are marked as public
        if getattr(route.endpoint, "is_public", False):
            continue

        # Check if auth is already in the route's dependencies
        has_auth = any(
            getattr(dependency.dependency, "__name__", "") == "verify"
            for dependency in route.dependencies
        )

        # Check if logging is already in the route's dependencies
        has_logging = any(
            getattr(dependency.dependency, "__name__", "") == log_dependency.__name__
            for dependency in route.dependencies
        )

        # Add auth dependency if needed
        if not has_auth:
            route.dependencies.append(Security(auth_dependency))

        # Add logging dependency if not already present
        if not has_logging:
            route.dependencies.append(Depends(log_dependency))
