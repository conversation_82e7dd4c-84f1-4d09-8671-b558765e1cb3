import tomllib

import matplotlib.pyplot as plt
import mlflow
import numpy as np
import pandas as pd
from dotenv import load_dotenv

from app.definitions import get_project_root
from app.logger import logger
from app.services.mlflow import (
    get_mlflow_tracking_uri,
    load_dataset_from_mlflow,
    load_run_from_mlflow,
    log_figure_to_mlflow,
    log_metrics_to_mlflow,
)
from app.visualizations import create_timeseries_plot, plot_prediction_differences

load_dotenv()

seed = 42
np.random.seed(seed)

ROOT_DIR = get_project_root()


def evaluate_and_log_metrics(data, targets, predictions, model_type="regressor"):
    """Helper function to evaluate and log metrics."""
    results = mlflow.evaluate(
        data=data,
        targets=targets,
        predictions=predictions,
        model_type=model_type,
    )
    log_metrics_to_mlflow(results.metrics)


def log_visualization_and_data(df):
    """Helper function to create and log visualizations and data."""
    # Log histogram
    plot_prediction_differences(df["differences"])
    log_figure_to_mlflow(plt.gcf(), "prediction_differences_histogram.png")
    plt.close()

    # Create and log the time series plot with both actual and forecast values
    create_timeseries_plot(data=df, forecast=df, x_column="date", y_column="ground_truth", forecast_column="forecast")
    log_figure_to_mlflow(plt.gcf(), "forecasts.png")
    plt.close()


def prepare_data(df):
    df["date"] = pd.to_datetime(df["date"])
    df = df[df["ground_truth"] != 0].reset_index(drop=True)
    df["differences"] = df["ground_truth"] - df["forecast"]
    return df


def evaluate_revenue_plans(experiment_name, run_name, **kwargs):  # noqa: ARG001
    try:
        # Set MLflow tracking URI to local directory
        mlflow.set_tracking_uri(get_mlflow_tracking_uri())
        run = load_run_from_mlflow(experiment_name, run_name)

        df = load_dataset_from_mlflow(run.run_id, "predictions.json")

        df = prepare_data(df)

        # Evaluate overall performance
        with mlflow.start_run(run_id=run.run_id):
            # Overall evaluation
            evaluate_and_log_metrics(
                data=df,
                targets="ground_truth",
                predictions="forecast",
            )
            log_visualization_and_data(df)

    except Exception as e:
        logger.error(f"Error in evaluation process: {str(e)}")
        raise


if __name__ == "__main__":
    with open(ROOT_DIR / "app" / "configs" / "evaluation_config.toml", "rb") as f:
        config = tomllib.load(f)

    evaluate_revenue_plans(**config)
