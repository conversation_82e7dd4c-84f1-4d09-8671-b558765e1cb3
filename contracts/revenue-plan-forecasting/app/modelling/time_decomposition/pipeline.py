import tomllib

from app.definitions import get_project_root
from app.logger import logger
from app.modelling.time_decomposition.evaluate import evaluate_revenue_plans
from app.modelling.time_decomposition.predict import predict_revenue_plans
from app.modelling.time_decomposition.train import train_revenue_plans

ROOT_DIR = get_project_root()


def run_pipeline():
    try:
        with open(ROOT_DIR / "app" / "configs" / "pipeline.toml", "rb") as f:
            config = tomllib.load(f)

        # Step 1: Training
        logger.info("Starting model training...")
        train_revenue_plans(**config)
        logger.info("Training completed successfully")

        # Step 2: Prediction
        logger.info("Starting prediction...")
        predict_revenue_plans(**config)
        logger.info("Prediction completed successfully")

        # Step 3: Evaluation
        logger.info("Starting evaluation...")
        evaluate_revenue_plans(**config)
        logger.info("Evaluation completed successfully")

        logger.info("Pipeline completed successfully!")

    except Exception as e:
        logger.error(f"Error in pipeline execution: {str(e)}")
        raise


if __name__ == "__main__":
    run_pipeline()
