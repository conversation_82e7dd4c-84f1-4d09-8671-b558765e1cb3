import tomllib

import matplotlib.pyplot as plt
import mlflow

from app.definitions import get_project_root
from app.etl.data_loader import load_data_from_s3
from app.logger import logger
from app.methods.prophet import prepare_data_for_prophet, train_prophet_model
from app.methods.sarima import prepare_data_for_sarima, train_sarima_model
from app.methods.xgboost import prepare_data_for_xgboost, train_xgboost_model
from app.services.mlflow import get_mlflow_tracking_uri, log_figure_to_mlflow
from app.visualizations import create_timeseries_plot

ROOT_DIR = get_project_root()


def prepare_data(df, model):
    if model == "prophet":
        return prepare_data_for_prophet(df)
    if model == "sarima":
        return prepare_data_for_sarima(df)
    if model == "xgboost":
        return prepare_data_for_xgboost(df)
    raise ValueError(f"Model {model} not supported")


def train_model(source_data, model_params, model, use_promos):
    if model == "prophet":
        return train_prophet_model(source_data, model_params, use_promos)
    if model == "sarima":
        return train_sarima_model(source_data, model_params)
    if model == "xgboost":
        return train_xgboost_model(source_data, model_params)
    raise ValueError(f"Model {model} not supported")


def log_timeseries_plot(data, forecast, model):
    # Create and log the time series plot with both actual and forecast values
    if model == "sarima":
        data = data.reset_index()
        forecast = forecast.reset_index()
        forecast.rename(columns={"index": "date", "predicted_mean": "yhat"}, inplace=True)
        create_timeseries_plot(
            data=data,
            forecast=forecast,
            x_column="date",
            y_column="daily_contribution",
            forecast_column="yhat",
        )
    elif model == "xgboost":
        data.reset_index(inplace=True)
        data["forecast"] = forecast
        create_timeseries_plot(
            data=data,
            forecast=data,
            x_column="date",
            y_column="daily_contribution",
            forecast_column="forecast",
        )
    else:
        create_timeseries_plot(
            data=data,
            forecast=forecast,
        )
    log_figure_to_mlflow(plt.gcf(), "historical.png")


def train_revenue_plans(model, client, experiment_name, run_name, model_params, use_promos, train_path, **kwargs):  # noqa: ARG001
    try:
        df = load_data_from_s3(train_path)

        # Set MLflow tracking URI to local directory
        mlflow.set_tracking_uri(get_mlflow_tracking_uri())

        # Set MLflow experiment
        mlflow.set_experiment(experiment_name)

        # Prepare data for this source
        source_data = prepare_data(df, model)
        # Create a parent run for the experiment
        with mlflow.start_run(run_name=run_name):
            mlflow.log_param("model_type", model)

            # Save and log the dataset
            mlflow.log_table(source_data, "training_data.json")

            # Train model
            trained_model, forecast = train_model(source_data, model_params, model, use_promos)

            # Log timeseries plot
            log_timeseries_plot(source_data, forecast, model)

            logger.info(f"Successfully trained model for {client}")

    except Exception as e:
        logger.error(f"Error training model for {client}: {str(e)}")
        raise e


if __name__ == "__main__":
    with open(ROOT_DIR / "app" / "configs" / "training_config.toml", "rb") as f:
        config = tomllib.load(f)

    train_revenue_plans(**config)
