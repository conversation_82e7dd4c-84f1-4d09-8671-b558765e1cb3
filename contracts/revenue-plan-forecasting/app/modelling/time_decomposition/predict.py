import tomllib

import mlflow
import pandas as pd

from app.definitions import get_project_root
from app.etl.data_loader import load_data_from_s3
from app.logger import logger
from app.methods.sarima import make_future_predictions
from app.methods.xgboost import make_xgboost_predictions
from app.services.mlflow import get_mlflow_tracking_uri, load_mlflow_model, load_run_from_mlflow

ROOT_DIR = get_project_root()


def make_predictions(model, data, model_name, use_promos):
    """Make predictions for all sources using the loaded models."""
    predictions = []

    try:
        # Prepare dates for prediction
        future_df = pd.DataFrame({"ds": data["date"]})

        # Make predictions
        if model_name == "prophet":
            if use_promos:
                promo_events = pd.read_csv(ROOT_DIR / "data" / "promos" / "promos_test.csv", index_col=0)
                promo_events["ds"] = pd.to_datetime(promo_events["ds"])
                future_events = pd.concat([model.holidays, promo_events])
                model.holidays = future_events
                forecast = model.predict(future_df)
            else:
                forecast = model.predict(future_df)

        elif model_name == "xgboost":
            forecast = make_xgboost_predictions(model, future_df)
        else:
            forecast, forecast_ci = make_future_predictions(model, steps=len(future_df))
            forecast = forecast.reset_index()
            forecast.rename(columns={"index": "ds", "predicted_mean": "yhat"}, inplace=True)
            forecast["yhat"] = forecast["yhat"].round(0).astype(int)

        # Combine predictions with test data
        for _, row in data.iterrows():
            pred_row = forecast[forecast["ds"] == row["date"]].iloc[0]
            predictions.append(
                {
                    "date": row["date"].strftime("%Y-%m-%d"),
                    "forecast": round(pred_row["yhat"]),
                    "ground_truth": row["daily_contribution"],
                }
            )
    except Exception as e:
        logger.error(f"Error making predictions: {str(e)}")
        raise

    # Convert to DataFrame
    return pd.DataFrame(predictions)


def predict_revenue_plans(model, experiment_name, run_name, test_path, use_promos, **kwargs):  # noqa: ARG001
    """Main function to load models and make predictions."""
    try:
        # Load data
        logger.info("Loading test data...")
        test_data = load_data_from_s3(test_path)
        test_data["date"] = pd.to_datetime(test_data["date"])

        # Set MLflow tracking URI to local directory
        mlflow.set_tracking_uri(get_mlflow_tracking_uri())

        # Load models
        logger.info("Loading models from MLflow...")
        run = load_run_from_mlflow(experiment_name, run_name)

        trained_model = load_mlflow_model(model, f"runs:/{run.run_id}/model")

        # Make predictions
        logger.info("Making predictions...")
        predictions = make_predictions(trained_model, test_data, model, use_promos)

        # log predictions to mlflow
        with mlflow.start_run(run_id=run.run_id):
            mlflow.log_table(predictions, "predictions.json")

    except Exception as e:
        logger.error(f"Error in prediction process: {str(e)}")
        raise


if __name__ == "__main__":
    with open(ROOT_DIR / "app" / "configs" / "prediction_config.toml", "rb") as f:
        config = tomllib.load(f)

    predict_revenue_plans(**config)
