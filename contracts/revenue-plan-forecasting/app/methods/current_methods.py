import argparse
import os
import sys
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.data_proccessing.data_prep import prepare_data
from app.definitions import get_project_root

ROOT_DIR = get_project_root()



seed = 42
np.random.seed(seed)

def calculate_baseline_forecast(df, method="random"):
    if method == "random":
        # random baseline forecast
        df["forecast"] = np.random.randint(0, 100, size=df.shape[0])
    elif method == "constant":
        # constant baseline forecast
        df["forecast"] = 50
    return df

def calculate_historical_contribution(df,
                                      start_date,
                                      end_date,
                                      days_prior=60):

    # Calculate rolling 60-day revenue for each category and date
    def get_category_revenue(group):
        current_date = group.name[0]  # date from the group key
        category = group.name[1]      # category from the group key
        source = group.name[2]        # source from the group key

        # Skip computation if before compute_from_date
        if start_date and current_date < start_date:
            return 0

        # Get total revenue for this source in the rolling window
        total_mask = (
            (df['source'] == source) &
            (df['date'] < current_date) &
            (df['date'] >= current_date - timedelta(days=days_prior))
        )
        total_revenue = df[total_mask]['revenue'].sum()

        # Get revenue for this category within this source in the rolling window
        category_mask = (
            (df['source'] == source) &
            (df['dim'] == category) &
            (df['date'] < current_date) &
            (df['date'] >= current_date - timedelta(days=days_prior))
        )
        category_revenue = df[category_mask]['revenue'].sum()

        return category_revenue / total_revenue if total_revenue != 0 else 0

    # Group by date, category, and source, then apply the calculation
    contributions = df.groupby(['date', 'dim', 'source']).apply(get_category_revenue).reset_index()
    contributions.columns = ['date', 'dim', 'source', 'forecast']
    return contributions[(contributions['date'] >= start_date) &
                                (contributions['date'] <= end_date)].reset_index(drop=True)



if __name__ == "__main__":

    parser = argparse.ArgumentParser(description='Process data with date range')
    parser.add_argument('--start_date', type=str, default='2025-01-01',
                        help='Start date in YYYY-MM-DD format')
    parser.add_argument('--end_date', type=str, default='2025-02-15',
                        help='End date in YYYY-MM-DD format')

    parser.add_argument('--input_file', type=str, default='data/combined_raw_data.csv',
                        help='Path to input CSV file')

    args = parser.parse_args()

    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')

    data = pd.read_csv(ROOT_DIR / args.input_file)

    filtered_data = prepare_data(data, start_date, end_date)

    historical_contributions = calculate_historical_contribution(filtered_data,
                                                              start_date,
                                                              end_date)
    merged_data = pd.merge(filtered_data,
                          historical_contributions,
                          on=['date', 'dim', 'source'],
                          how='left')
    merged_data = merged_data.dropna().reset_index(drop=True)
    merged_data['forecast'] = (pd.to_numeric(merged_data['forecast']) * 100).round().astype(int)
    merged_data['ground_truth'] = pd.to_numeric(merged_data['ground_truth']).astype(int)
    merged_data.to_csv(ROOT_DIR / "data" / f"current_method_{start_date}_{end_date}.csv", index=False)
