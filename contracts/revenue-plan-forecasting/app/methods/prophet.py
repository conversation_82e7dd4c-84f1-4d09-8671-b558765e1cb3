import numpy as np
import pandas as pd
from prophet import Prophet
from sklearn.metrics import mean_absolute_error, mean_squared_error

from app.definitions import get_project_root
from app.services.mlflow import log_metrics_to_mlflow, log_model_to_mlflow, log_params_to_mlflow

ROOT_DIR = get_project_root()


def prepare_data_for_prophet(df):
    """Prepare data for Prophet model for a specific source."""
    return df.rename(columns={"date": "ds", "daily_contribution": "y"})


def train_prophet_model(train_data, prophet_params, use_promos=False):
    """Train a Prophet model for a specific source."""
    # Log parameters
    log_params_to_mlflow({"model_type": "prophet", "use_promos": use_promos, **prophet_params})

    if use_promos:
        # Create the DataFrame with specified dates
        promo_events = pd.read_csv(ROOT_DIR / "data" / "promos" / "promos_train.csv", index_col=0)
        model = Prophet(**prophet_params, holidays=promo_events)

    else:
        # Initialize and train Prophet model
        model = Prophet(**prophet_params)

    model.add_country_holidays(country_name="US")

    model.fit(train_data)

    # Make predictions on training data
    forecast = model.predict(train_data)

    # Calculate metrics
    mse = mean_squared_error(train_data["y"], forecast["yhat"])
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(train_data["y"], forecast["yhat"])

    # Log metrics
    log_metrics_to_mlflow({"mse": mse, "rmse": rmse, "mae": mae})

    # Log the model
    log_model_to_mlflow(model, "prophet", input_example=train_data.iloc[[0]])

    return model, forecast
