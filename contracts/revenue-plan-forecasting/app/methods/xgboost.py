import numpy as np
import pandas as pd
import xgboost as xgb
from sklearn.metrics import mean_absolute_error, mean_squared_error

from app.definitions import get_project_root
from app.services.mlflow import log_metrics_to_mlflow, log_model_to_mlflow, log_params_to_mlflow

ROOT_DIR = get_project_root()


def prepare_data_for_xgboost(df):
    """Prepare data for XGBoost model."""
    source_df = df.copy()

    # Convert date to features
    source_df["year"] = pd.to_datetime(source_df["date"]).dt.year
    source_df["month"] = pd.to_datetime(source_df["date"]).dt.month
    source_df["day"] = pd.to_datetime(source_df["date"]).dt.day
    source_df["dayofweek"] = pd.to_datetime(source_df["date"]).dt.dayofweek

    # Create feature matrix X and target y
    return source_df[["date", "year", "month", "day", "dayofweek", "daily_contribution"]]


def make_xgboost_predictions(model, test_data):
    """Make predictions using an XGBoost model."""
    # Convert date features
    X_test = pd.DataFrame()
    X_test["year"] = pd.to_datetime(test_data["ds"]).dt.year.astype("int32")
    X_test["month"] = pd.to_datetime(test_data["ds"]).dt.month.astype("int32")
    X_test["day"] = pd.to_datetime(test_data["ds"]).dt.day.astype("int32")
    X_test["dayofweek"] = pd.to_datetime(test_data["ds"]).dt.dayofweek.astype("int32")

    # Create DMatrix for prediction
    dtest = xgb.DMatrix(X_test)

    # Make predictions
    predictions = model.predict(dtest)

    # Create a result DataFrame with dates and predictions
    return pd.DataFrame({"ds": test_data["ds"], "yhat": predictions})


def train_xgboost_model(train_data, xgb_params, use_promos=False):
    """Train an XGBoost model."""
    # Log parameters
    log_params_to_mlflow({"model_type": "xgboost", "use_promos": use_promos, **xgb_params})

    X = train_data.drop(columns=["daily_contribution"])
    y = train_data["daily_contribution"]

    if use_promos:
        # Load promotion data
        pd.read_csv(ROOT_DIR / "data" / "promos" / "promos_train.csv", index_col=0)
        # Add promo features to X (you'll need to implement this based on your promo data structure)
        # X = add_promo_features(X, promo_events)

    # Create DMatrix for XGBoost
    dtrain = xgb.DMatrix(X.drop(["date"], axis=1), label=y, enable_categorical=True)

    # Train model
    model = xgb.train(xgb_params, dtrain)

    # Make predictions on training data
    predictions = model.predict(dtrain)

    # Calculate metrics
    mse = mean_squared_error(y, predictions)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y, predictions)

    # Log metrics
    log_metrics_to_mlflow({"mse": mse, "rmse": rmse, "mae": mae})

    # Log the model
    input_example = X.drop(columns=["date"]).iloc[[0]]
    log_model_to_mlflow(model, "xgboost", input_example=input_example)

    return model, predictions
