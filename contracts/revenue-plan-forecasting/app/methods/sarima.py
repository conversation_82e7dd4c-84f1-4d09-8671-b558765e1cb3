import numpy as np
from sklearn.metrics import mean_absolute_error, mean_squared_error
from statsmodels.tsa.statespace.sarimax import SARIMAX

from app.services.mlflow import log_metrics_to_mlflow, log_model_to_mlflow, log_params_to_mlflow


def prepare_data_for_sarima(df):
    """Prepare data for SARIMA model."""
    # SARIMA requires the data to be sorted by date
    df = df.sort_values("date")
    # Set date as index for time series analysis
    df = df.set_index("date")
    return df["daily_contribution"]


def train_sarima_model(train_data, sarima_params):
    """Train a SARIMA model."""
    # Log parameters
    log_params_to_mlflow({"model_type": "sarima", **sarima_params})

    # Initialize and train SARIMA model
    model = SARIMAX(train_data, freq="D", **sarima_params)

    # Fit the model
    fitted_model = model.fit(disp=False)

    # Make predictions on training data
    forecast = fitted_model.get_prediction(start=0).predicted_mean

    # Calculate metrics
    mse = mean_squared_error(train_data, forecast)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(train_data, forecast)

    # Log metrics
    log_metrics_to_mlflow({"mse": mse, "rmse": rmse, "mae": mae})

    # Log the model
    log_model_to_mlflow(fitted_model, "sarima", input_example=train_data.iloc[[0]])

    return fitted_model, forecast


def make_future_predictions(model, steps):
    """Make future predictions using the trained SARIMA model."""
    forecast = model.get_forecast(steps=steps)
    forecast_mean = forecast.predicted_mean
    forecast_ci = forecast.conf_int()

    return forecast_mean, forecast_ci


# Example usage:
if __name__ == "__main__":
    # Example parameters
    sarima_params = {
        "order": (1, 1, 1),  # (p, d, q)
        "seasonal_order": (1, 1, 1, 12),  # (P, D, Q, s)
        "enforce_stationarity": False,
        "enforce_invertibility": False,
    }

    # Example:
    # df = pd.read_csv('your_data.csv')
    # source = 'your_source'

    # with mlflow.start_run(run_name="SARIMA_Model"):
    #     train_data = prepare_data_for_sarima(df, source)
    #     model, forecast = train_sarima_model(train_data, source, sarima_params)
    #
    #     # Make future predictions
    #     future_steps = 30
    #     future_forecast, confidence_intervals = make_future_predictions(model, future_steps)
