import argparse
import os
import sys
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from data_proccessing.data_prep import prepare_data

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from definitions import get_project_root

ROOT_DIR = get_project_root()

seed = 42
np.random.seed(seed)


def compute_historical_contributions(df):
    # Convert date to datetime and create ISO calendar columns
    df["datetime"] = pd.to_datetime(df["date"])
    df["iso_week"] = df["datetime"].dt.isocalendar().week
    df["iso_year"] = df["datetime"].dt.isocalendar().year
    df["iso_day"] = df["datetime"].dt.isocalendar().day

    # Create historical mapping dataframe with only needed columns
    historical_data = df[["iso_year", "iso_week", "iso_day", "source", "daily_contribution"]].copy()
    historical_data["iso_year"] = historical_data["iso_year"] + 1

    # Merge to get historical contributions
    result = df.merge(
        historical_data[["iso_year", "iso_week", "iso_day", "source", "daily_contribution"]],
        left_on=["iso_year", "iso_week", "iso_day", "source"],
        right_on=["iso_year", "iso_week", "iso_day", "source"],
        how="left",
        suffixes=("", "_historical"),
    )

    # Clean up temporary columns
    return result[["date", "source", "revenue", "daily_contribution", "daily_contribution_historical"]]



def filter_data(df, start_date, end_date, days_prior=366):
    # Filter data between start and end date
    df = df[df["date"] >= start_date - timedelta(days=days_prior)]  # Include 365 days before start_date
    df = df[df["date"] <= end_date]
    return df.reset_index(drop=True)


def prepare_data(data, start_date, end_date):
    data = data[data["source"] != "hottopic"].reset_index(drop=True)  # hotopic is not a valid source

    # Convert date column to datetime
    data["date"] = pd.to_datetime(data["date"])

    return filter_data(data, start_date, end_date)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process data with date range")
    parser.add_argument("--start_date", type=str, default="2025-01-01", help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end_date", type=str, default="2025-02-15", help="End date in YYYY-MM-DD format")

    parser.add_argument(
        "--input_file", type=str, default="data/contribution_per_date_weekly.csv", help="Path to input CSV file"
    )

    args = parser.parse_args()

    start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
    end_date = datetime.strptime(args.end_date, "%Y-%m-%d")

    data = pd.read_csv(ROOT_DIR / args.input_file)

    filtered_data = prepare_data(data, start_date, end_date)

    historical_contributions = compute_historical_contributions(filtered_data)
    historical_contributions = filter_data(historical_contributions, start_date, end_date, days_prior=0)

    historical_contributions["forecast"] = (
        (pd.to_numeric(historical_contributions["daily_contribution_historical"])).round().astype(int)
    )
    historical_contributions["ground_truth"] = pd.to_numeric(historical_contributions["daily_contribution"]).astype(int)
    historical_contributions = historical_contributions[["date", "source", "forecast", "ground_truth"]]
    historical_contributions.to_csv(
        ROOT_DIR / "data" / f"time_dec_current_method_per_week{start_date}_{end_date}.csv", index=False
    )
