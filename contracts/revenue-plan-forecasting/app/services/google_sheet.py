from typing import Any

import gspread
import pandas as pd
from google.oauth2.credentials import Credentials
from tenacity import retry, stop_after_attempt, wait_fixed


def access_google_sheet(
    spreadsheet_id: str,
    sheet_name: str,
    creds: dict[str, str | dict[str, str]],
) -> list[list[str]]:
    """Access a Google Sheet using the specified method.

    Args:
        spreadsheet_id (str): The ID of the Google Sheet.
        sheet_name (str): The name of the sheet within the Google Sheet.
        method (str): The access method to use. Valid options are "google_api" or "gspread".
        creds (dict[str, str | dict[str, str]]): The credentials required for accessing the Google Sheet.

    Returns:
        list[list[str]]: The data from the Google Sheet as a list of lists.

    Raises:
        ValueError: If an invalid access method is provided.
    """
    gspread_creds = Credentials.from_authorized_user_info(
        {
            "client_id": creds.get("clientId"),
            "client_secret": creds.get("clientSecret"),
            "refresh_token": creds.get("refresh_token"),
        },
        scopes=["https://www.googleapis.com/auth/spreadsheets.readonly"],
    )
    return access_gsheet_via_python_gspread(
        spreadsheet_id,
        sheet_name,
        gspread_creds,
    )



# TODO: make this async
@retry(stop=stop_after_attempt(3), wait=wait_fixed(10) + wait_fixed(20) + wait_fixed(30))
def access_gsheet_via_python_gspread(
    spreadsheet_id: str,
    sheet_name: str,
    creds: dict[str, Any],
) -> list[list[str]]:
    """Access a Google Sheet via the gspread Python library.

    Args:
        spreadsheet_id: The ID of the Google Sheet.
        sheet_name: The name of the sheet within the Google Sheet.
        creds: The credentials for gspread.

    Returns:
        A list of lists representing the rows and columns of the Google Sheet.
    """
    # Authorize the gspread client with your credentials
    gc = gspread.authorize(creds)

    # Open the spreadsheet and select the worksheet
    spreadsheet = gc.open_by_key(spreadsheet_id).worksheet(sheet_name)

    # Get all values from the worksheet
    return spreadsheet.get_all_values()


def google_sheet_to_pandas(google_sheet: list[list[str]]) -> pd.DataFrame:
    """Post-process a Google Sheet.

    Args:
        google_sheet: A list of lists representing the rows and columns of the Google Sheet.

    Returns:
        A pandas DataFrame representing the Google Sheet.
    """
    return pd.DataFrame(google_sheet[1:], columns=google_sheet[0])


def store_dataframe_to_gsheet(
    df: pd.DataFrame,
    spreadsheet_id: str,
    sheet_name: str,
    creds: dict[str, str | dict[str, str]],
) -> None:
    """Store a pandas DataFrame in a Google Sheet.

    Args:
        df: The pandas DataFrame to store.
        spreadsheet_id: The ID of the Google Sheet.
        sheet_name: The name of the sheet within the Google Sheet.
        creds: The credentials required for accessing the Google Sheet.
    """
    gspread_creds = Credentials.from_authorized_user_info(
        {
            "client_id": creds.get("clientId"),
            "client_secret": creds.get("clientSecret"),
            "refresh_token": creds.get("refresh_token"),
        },
        scopes=["https://www.googleapis.com/auth/spreadsheets"],
    )

    # Authorize the gspread client
    gc = gspread.authorize(gspread_creds)

    # Open the spreadsheet and select/create the worksheet
    spreadsheet = gc.open_by_key(spreadsheet_id)
    try:
        worksheet = spreadsheet.worksheet(sheet_name)
    except gspread.WorksheetNotFound:
        worksheet = spreadsheet.add_worksheet(sheet_name, rows=1, cols=1)

    # Convert DataFrame to list of lists, including headers
    data = [df.columns.tolist()] + df.values.tolist()

    # Clear existing content and update with new data
    worksheet.clear()
    # Replace NaN values with empty strings before updating
    data = [["" if pd.isna(cell) else cell for cell in row] for row in data]
    worksheet.update(data)


if __name__ == "__main__":
    import os

    from dotenv import load_dotenv

    load_dotenv()
    gsheet_input = {
        "credentials": {
            "clientId": os.environ["GA4_CLIENT_ID"],
            "clientSecret": os.environ["GA4_CLIENT_SECRET"],
            "refresh_token": os.environ["GA4_REFRESH_TOKEN"],
        },
        "spreadsheet_id": "1MC0-7WmIE-2v210yEabcwqvFPMV4fCUP6uWUN_w5EvE",
        "sheet_name": "all_data",
    }

    # or
    # gsheet_input = load_json("PATH_TO_INPUT")
    google_sheet = access_google_sheet(
        gsheet_input.get("spreadsheet_id"),
        gsheet_input.get("sheet_name"),
        gsheet_input.get("credentials"),
    )

    google_sheet_processed = google_sheet_to_pandas(google_sheet)
    print(google_sheet_processed)
