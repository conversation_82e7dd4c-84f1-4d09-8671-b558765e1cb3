import json
import os

import mlflow
import pandas as pd

MLFLOW_TRACKING_URI = {
    "prod": "https://mlflow.baresquare.com/",
}.get(os.environ["PL_ENV"], "https://mlflow.test.baresquare.com/")


def get_mlflow_tracking_uri():
    return MLFLOW_TRACKING_URI


def load_mlflow_experiment(experiment_name):
    experiment = mlflow.get_experiment_by_name(experiment_name)
    if not experiment:
        raise ValueError(f"Experiment {experiment_name} not found")
    return experiment


def load_mlflow_model(model_name, model_path):
    if model_name == "prophet":
        return mlflow.prophet.load_model(model_path)
    if model_name == "sarima":
        return mlflow.statsmodels.load_model(model_path)
    if model_name == "xgboost":
        return mlflow.xgboost.load_model(model_path)
    raise ValueError(f"Model {model_name} not supported")


def load_run_from_mlflow(experiment_name, run_name):
    experiment = load_mlflow_experiment(experiment_name)
    runs = mlflow.search_runs(experiment_ids=[experiment.experiment_id])
    # Sort runs by end_time in descending order and get the latest run
    return runs[runs["tags.mlflow.runName"] == run_name].sort_values(by="end_time", ascending=False).iloc[0]


def log_table(table, table_name):
    mlflow.log_table(table, table_name)


def log_artifact_to_mlflow(artifact_path):
    mlflow.log_artifact(artifact_path)

    if os.path.exists(artifact_path):
        os.remove(artifact_path)


def log_metrics_to_mlflow(metrics):
    metrics = {metric: round(value, 3) for metric, value in metrics.items()}
    mlflow.log_metrics(metrics)


def log_model_to_mlflow(model, model_name, input_example):
    if model_name == "prophet":
        mlflow.prophet.log_model(model, "model", input_example=input_example)
        return
    if model_name == "sarima":
        mlflow.statsmodels.log_model(model, "model", input_example=input_example)
        return
    if model_name == "xgboost":
        mlflow.xgboost.log_model(model, "model", input_example=input_example)
        return
    raise ValueError(f"Model {model_name} not supported")


def log_params_to_mlflow(params):
    mlflow.log_params(params)


def load_dataset_from_mlflow(run_id, artifact_path):
    local_path = mlflow.artifacts.download_artifacts(run_id=run_id, artifact_path=artifact_path)

    # Read into memory
    with open(local_path, "r") as f:
        raw = json.load(f)

    os.remove(local_path)
    return pd.DataFrame(raw["data"], columns=raw["columns"])


def log_figure_to_mlflow(figure, figure_name):
    mlflow.log_figure(figure, figure_name)
