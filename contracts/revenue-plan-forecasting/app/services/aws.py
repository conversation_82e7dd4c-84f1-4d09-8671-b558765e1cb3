import json
import os
from pathlib import Path

import boto3
from botocore.exceptions import ClientError

from app.exceptions import ExceptionInfo
from app.logger import logger


def get_ssm_client():
    """Return an SSM client using the Singleton Pattern.

    :return: SSM client
    """
    global ssm_client
    if "ssm_client" not in globals() or ssm_client is None:
        if os.getenv("AWS_PROFILE") is not None:
            session = boto3.Session(profile_name=os.getenv("AWS_PROFILE"))
            ssm_client = session.client(service_name="ssm", region_name=os.getenv("PL_REGION"))
        else:
            ssm_client = boto3.client(service_name="ssm", region_name=os.getenv("PL_REGION", "us-east-1"))
    return ssm_client


def get_ssm_parameter(ssm_key: str, return_json: bool = False, **_) -> str | dict:
    """Retrieve parameter from AWS SSM.

    Args:
        ssm_key (str): The path to the parameter in AWS SSM
        return_json (bool): Whether to return the parameter as a json object
                            Defaults to False

    Returns:
        str | dict: The parameter value as a string or dictionary
    """
    logger.debug(f"Retrieving SSM param {ssm_key}")
    try:
        ssm_parameter = get_ssm_client().get_parameter(Name=ssm_key, WithDecryption=True)["Parameter"]["Value"]
        if return_json:
            return json.loads(ssm_parameter)
        return ssm_parameter
    except ClientError as e:
        logger.warning(f"Failed to retrieve SSM param from {ssm_key}")
        raise ExceptionInfo(
            msg=f"Failed to retrieve SSM param from {ssm_key}",
            data={
                "ssm_parameter_name": ssm_key,
            },
        ) from e


def put_ssm_parameter(ssm_key: str, ssm_value: str, overwrite: bool, ssm_type="SecureString"):
    """Put parameter to AWS SSM.

    Args:
        ssm_key (str): The path to the parameter in AWS SSM
        ssm_value (str): The value of the SSM parameter
        overwrite (bool): Whether to overwrite an existing value for the SSM parameter
        ssm_type (str): one of 'String'|'StringList'|'SecureString'

    Returns:
        dict: Example: {'Version': 123,'Tier': 'Standard'|'Advanced'|'Intelligent-Tiering'}
    """
    logger.debug(f"Retrieving SSM param {ssm_key}")
    ssm_client = boto3.client(service_name="ssm", region_name=os.getenv("PL_REGION", "us-east-1"))
    try:
        ssm_client.put_parameter(Name=ssm_key, Value=ssm_value, Type=ssm_type, Overwrite=overwrite)
    except ClientError as error:
        logger.error(f"Failed to put SSM param {ssm_key}")
        raise error


class S3Client:
    def __init__(self):
        aws_profile = os.getenv("AWS_PROFILE")
        region = os.getenv("PL_REGION", "us-east-1")
        if aws_profile is not None:
            session = boto3.Session(profile_name=aws_profile)
            self.client = session.client("s3", region_name=region)
        else:
            self.client = boto3.client("s3", region_name=region)

    def download_file(self, bucket: str, key: str, local_path: Path) -> bool:
        """Download a file from S3 to a local path.
        Returns True if successful, False otherwise.
        """
        try:
            self.client.download_file(bucket, key, str(local_path))
            return True
        except ClientError as e:
            logger.error(f"Error downloading from S3: {str(e)}")
            return False

    def upload_file(self, local_path: Path, bucket: str, key: str) -> bool:
        """Upload a file to S3 from a local path.
        Returns True if successful, False otherwise.
        """
        try:
            self.client.upload_file(str(local_path), bucket, key)
            return True
        except ClientError as e:
            logger.error(f"Error uploading to S3: {str(e)}")
            return False

    def upload_fileobj(self, fileobj, bucket: str, key: str) -> bool:
        """Upload a file-like object to S3.
        Returns True if successful, False otherwise.
        """
        try:
            self.client.upload_fileobj(fileobj, bucket, key)
            return True
        except ClientError as e:
            logger.error(f"Error uploading file-like object to S3: {str(e)}")
            return False

    def read_object_as_string(self, bucket: str, key: str) -> str | None:
        try:
            obj = self.client.get_object(Bucket=bucket, Key=key)
            return obj["Body"].read().decode("utf-8")
        except (ClientError, UnicodeDecodeError) as e:
            logger.error(f"Error reading or decoding object from S3: {str(e)}")
            return None
