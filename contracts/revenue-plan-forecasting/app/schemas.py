import json
from datetime import date, datetime
from typing import List, Literal

from pydantic import BaseModel, Field, ValidationError, confloat


class HistoricalData(BaseModel):
    date: date
    revenue: int
    special_date: str = Field(default_factory=str)


class ForecastData(BaseModel):
    date: date
    revenue: int
    lower: int
    upper: int
    contribution: confloat(ge=0, le=1)
    special_date: str = Field(default_factory=str)


class RevenueGroup(BaseModel):
    name: str
    historical: List[HistoricalData]
    forecast: List[ForecastData]


class KeyMetric(BaseModel):
    id: str
    name: str
    type: str
    currency: str
    symbol: str


class RevenuePlanOutput(BaseModel):
    summary: str
    period_start: date
    forecast_window: Literal["daily", "weekly", "monthly"]
    run_date: datetime
    next_run_date: datetime
    execution_id: str
    org_id: str
    key_metric: KeyMetric
    revenue_groups: List[RevenueGroup]


if __name__ == "__main__":
    # --- Load and validate the JSON data ---
    # USE THIS TO UP
    with open("contracts/forecast_data_example.json", encoding="utf-8") as f:
        data = json.load(f)

    try:
        forecast = RevenuePlanOutput.model_validate(data)  # Pydantic v2+
        print("Validation successful!")
    except ValidationError as e:
        print("Validation failed:")
        print(e)

    schema = RevenuePlanOutput.model_json_schema()

    # Store the schema to a file
    with open("contracts/output_schema.json", "w", encoding="utf-8") as f:
        json.dump(schema, f, indent=2, ensure_ascii=False)
