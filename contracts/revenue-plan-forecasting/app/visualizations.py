import matplotlib.dates as mdates
import matplotlib.pyplot as plt


def create_timeseries_plot(data, forecast=None, x_column="ds", y_column="y", forecast_column="yhat"):
    """Create a time series plot showing actual values and optionally forecast values.

    Args:
        data (pd.DataFrame): DataFrame containing 'ds' (dates) and 'y' (values) columns
        forecast (pd.DataFrame, optional): Prophet forecast DataFrame containing 'ds', 'yhat',
                                        'yhat_lower', and 'yhat_upper' columns

    Returns:
        str: Filename of the saved plot
    """
    plt.figure(figsize=(12, 6))

    # Plot actual values
    plt.plot(data[x_column], data[y_column], label="Actual", color="blue", marker="o", markersize=2)

    # If forecast data is provided, plot it with confidence intervals
    if forecast is not None:
        plt.plot(forecast[x_column], forecast[forecast_column], label="Forecast", color="red", linestyle="--")
        # plt.fill_between(forecast[x_column],
        #                 forecast[forecast_column + '_lower'],
        #                 forecast[forecast_column + '_upper'],
        #                 color='red',
        #                 alpha=0.2,
        #                 label='Confidence Interval')

    plt.title(f"Time Series Plot")
    plt.xlabel("Date")
    plt.ylabel("Value")
    plt.grid(True, alpha=0.3)
    plt.legend()

    # Rotate x-axis labels for better readability
    # Format x-axis with better date formatting
    ax = plt.gca()
    ax.xaxis.set_major_locator(mdates.AutoDateLocator())
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%b %d"))  # Format: Jan 01, Feb 05, etc.

    plt.gcf().autofmt_xdate()  # Auto rotates and aligns dates
    plt.tight_layout()


def plot_prediction_differences(differences):
    plt.figure(figsize=(10, 6))
    plt.hist(differences, bins=50)
    plt.title(f"Distribution of Ground Truth - Forecast")
    plt.xlabel("Difference")
    plt.ylabel("Frequency")
