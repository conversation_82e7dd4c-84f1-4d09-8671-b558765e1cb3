import os
import traceback

from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.etl.etl_per_date import perform_etl_per_date
from app.exceptions import handle_exception
from app.logger import logger
from app.modelling.time_decomposition.predict import predict_revenue_plans
from app.modelling.time_decomposition.train import train_revenue_plans
from app.router_setup import public_route
from app.services import authentication

router_v1 = APIRouter()
auth = authentication.VerifyToken()


@router_v1.get("/status")
@public_route()
async def status_handler():
    """Return the SHA and build date."""
    try:
        m = {
            "status": "ok",
            "build_sha": os.getenv("BUILD_SHA"),
            "build_date": os.getenv("BUILD_DATE"),
            "build_tag": os.getenv("BUILD_TAG"),
        }
        logger.info("GET /status request", extra=m)
        return JSONResponse(status_code=200, content=m)
    except BaseException as e:
        response = {
            "error_message": str(e),
            "error-type": type(e).__name__,
            "traceback": traceback.format_exception(e),
        }
        logger.critical("Exception in status endpoint", extra=response)
        return JSONResponse(status_code=500, content=response)


# TODO: Consider the input parameters as post instead of etl.toml
@router_v1.get(
    "/etl/per-date",
    summary="Trigger ETL for Revenue Data",
    description=(
        "Triggers an ETL process that reads the provided revenue data from a Google Sheet, "
        "processes it to compute daily contributions per revenue group, and stores the results in both Google Sheets and S3. "
        "This endpoint is intended for internal use to refresh the processed revenue data.\n\n"
        "The ETL process uses configuration from the `etl.toml` file"
    ),
    response_description="Confirmation that the ETL process was triggered.",
)
@public_route()
def etl_per_date():
    """Triggers the ETL process for daily revenue data.

    This endpoint initiates an ETL pipeline that:
    - Reads raw daily revenue data from a configured Google Sheet.
    - Processes the data to fill missing dates, compute weekly totals, and calculate daily contributions.
    - Stores the processed data back to a Google Sheet and uploads train/test splits to S3.

    Input:
        The ETL process uses configuration from the `etl.toml` file

    Returns:
        JSONResponse: A message confirming the ETL process was triggered, or an error message.
    """
    try:
        logger.info("GET /etl/per-date request")
        perform_etl_per_date()
        return JSONResponse(status_code=200, content={"message": "ETL per date request received"})
    except BaseException as e:
        response = handle_exception(e, "/etl/per-date")
        return JSONResponse(status_code=response["status"], content=response)


# TODO: Consider the input parameters as post instead of etl.toml
@router_v1.get(
    "/modelling/train",
    summary="Trigger Training of Revenue Plans",
    description=("Triggers the training of revenue plans for a specific client."),
    response_description="Confirmation that the training was triggered.",
)
@public_route()
def train_rp():
    """Triggers the training of revenue plans for a specific client.

    This endpoint initiates the training of revenue plans for a specific client.

    Input:
        The training process uses configuration from the `training_config.toml` file

    Returns:
        JSONResponse: A message confirming the training was triggered, or an error message.
    """
    try:
        logger.info("GET /modelling/train request")
        train_revenue_plans()
        return JSONResponse(status_code=200, content={"message": "Training completed"})
    except BaseException as e:
        response = handle_exception(e, "/modelling/train")
        return JSONResponse(status_code=response["status"], content=response)


# TODO: Consider the input parameters as post instead of etl.toml
@router_v1.get(
    "/modelling/predict",
    summary="Trigger Prediction of Revenue Plans",
    description=("Triggers the prediction of revenue plans for a specific client."),
    response_description="Confirmation that the prediction was triggered.",
)
@public_route()
def predict_rp():
    """Triggers the prediction of revenue plans for a specific client.

    This endpoint initiates the prediction of revenue plans for a specific client.

    Input:
        The prediction process uses configuration from the `prediction_config.toml` file

    Returns:
        JSONResponse: A message confirming the prediction was triggered, or an error message.
    """
    try:
        logger.info("GET /modelling/predict request")
        predict_revenue_plans()
        return JSONResponse(status_code=200, content={"message": "Prediction completed"})
    except BaseException as e:
        response = handle_exception(e, "/modelling/predict")
        return JSONResponse(status_code=response["status"], content=response)
