# Forecast UI

This folder contains code for a mock up UI for demonstration purposes. It uses the examples from contract folder (forecast_data_week17.json, forecast_data_week18.json, etc).

You need to install the following packages:

```bash
pip install streamlit plotly
```

Then you need to run the following command:

```bash
streamlit run forecast_ui.py
```

The data is loaded from the `contracts` folder.
