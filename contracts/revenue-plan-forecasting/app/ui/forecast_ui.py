import json
from datetime import datetime, timedelta
from io import StringIO

import pandas as pd
import plotly.colors
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st

# --- Configuration ---
# This MUST be the first Streamlit command
st.set_page_config(layout="wide")

# Initialize session state variables
if 'show_rerun_modal' not in st.session_state:
    st.session_state['show_rerun_modal'] = False
if 'show_upload' not in st.session_state:
    st.session_state['show_upload'] = False
if 'last_run' not in st.session_state:
    st.session_state['last_run'] = datetime.now()

# --- Apply custom CSS to soften the UI elements ---
st.markdown("""
<style>
    /* Soften the sidebar background */
    .css-1d391kg, .css-12oz5g7 {
        background-color: #f5f7f9;
    }

    /* Soften the multiselect dropdown colors */
    .stMultiSelect span[data-baseweb="tag"] {
        background-color: #e1e9f2 !important;
        color: #45536a !important;
    }

    /* Soften the multiselect dropdown hover state */
    .stMultiSelect span[data-baseweb="tag"]:hover {
        background-color: #d0dbe8 !important;
    }

    /* Make the selected item text color more subtle */
    .stMultiSelect [role="option"][aria-selected="true"] {
        background-color: #f0f4f9 !important;
        color: #45536a !important;
    }

    /* Soften the selectbox */
    .stSelectbox [data-baseweb="select"] {
        background-color: #f5f7f9;
    }

    /* Soften the main content area background slightly */
    .main .block-container {
        background-color: #ffffff;
    }

    /* More subtle buttons */
    .stButton button {
        background-color: #e7ecf1 !important;
        color: #45536a !important;
        border: none !important;
    }

    .stButton button:hover {
        background-color: #d0dbe8 !important;
        color: #344154 !important;
    }
</style>
""", unsafe_allow_html=True)

# --- Data Sources ---
# Map descriptive names to file paths
DATA_SOURCES = {
    "Week 17 (04/21/2025 - 04/27/2025)": "/home/<USER>/github/revenue-plan-forecasting/contracts/forecast_data_week17.json",
    "Week 18 (04/28/2025 - 05/04/2025)": "/home/<USER>/github/revenue-plan-forecasting/contracts/forecast_data_week18.json"
}

# --- Data Loading ---
# Cache data based on the filepath
@st.cache_data
def load_data(filepath):
    """Loads and preprocesses the forecast data from a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        st.error(f"Error: The file {filepath} was not found.")
        return None, None, None, None, None
    except json.JSONDecodeError:
        st.error(f"Error: Could not decode JSON from the file {filepath}.")
        return None, None, None, None, None

    forecast_week = data.get("forecast_week", "Unknown Week")
    summary_text = data.get("summary", "No summary available.")
    categories = data.get("categories", [])

    data_flat = []
    date_format = "%m/%d/%Y"

    for category in categories:
        cat_name = category.get("name", "Unknown Category")

        # Process historical data
        for entry in category.get("historical", []):
            entry['category'] = cat_name
            entry['type'] = 'Historical'
            entry['lower'] = None  # Ensure these columns exist for consistency
            entry['upper'] = None
            entry['contribution'] = None
            # Convert date, coercing errors
            entry['date'] = pd.to_datetime(entry.get('date'), format=date_format, errors='coerce')
            data_flat.append(entry)

        # Process forecast data
        for entry in category.get("forecast", []):
            entry['category'] = cat_name
            entry['type'] = 'Forecast'
            entry['contribution'] = entry.get('contribution')
            # Convert date, coercing errors
            entry['date'] = pd.to_datetime(entry.get('date'), format=date_format, errors='coerce')
            data_flat.append(entry)

    if not data_flat:
        st.warning("No data found in the file.")
        return forecast_week, summary_text, pd.DataFrame(), [], []

    df = pd.DataFrame(data_flat)

    # Drop rows where date conversion failed
    original_rows = len(df)
    df.dropna(subset=['date'], inplace=True)
    if len(df) < original_rows:
        st.warning(f"Dropped {original_rows - len(df)} rows due to invalid date format.")

    # Extract unique category names (excluding 'total' if present, handle later)
    category_list = df['category'].unique().tolist()

    return forecast_week, summary_text, df, category_list, data.get("categories")

# --- Sidebar Filters ---
st.sidebar.header("Filters")

# Week Selector
selected_week_key = st.sidebar.selectbox(
    "Select Forecast Period",
    list(DATA_SOURCES.keys()),
    index=0  # Default to the first item (Week 17)
)

# Load data based on selection
selected_filepath = DATA_SOURCES[selected_week_key]
forecast_week, summary_text, df, category_list, raw_categories = load_data(selected_filepath)

# If data loading failed, stop execution
if df is None or df.empty:
    st.stop()

# Revenue Group Selector
# Ensure 'total' is last if it exists
if "total" in category_list:
    category_list.remove("total")
    category_list.sort()  # Sort alphabetically
    category_list.append("total")
else:
    category_list.sort()

# Only 'total' selected by default
default_categories = ["total"]
selected_categories = st.sidebar.multiselect(
    "Select Revenue Groups",
    category_list,
    # Use session state to remember selection across data source changes if desired
    # For now, just reset to default when week changes
    default=default_categories
)

if not selected_categories:
    st.warning("Please select at least one revenue group.")
    st.stop()

# --- Main Panel ---

# --- 1. Summary ---
st.header("Summary")
st.markdown(summary_text)

# Filter DataFrame based on selected categories
filtered_plot_data = df[df['category'].isin(selected_categories)].copy()

# Add a check if filtered data is empty
if filtered_plot_data.empty:
    st.warning("No data available for the selected revenue groups in this forecast period.")
    st.stop()

# --- 2. Time Series Graph ---
st.header("Revenue plans forecasts")

fig = go.Figure()

# Ensure consistent colors for categories across trace types
# Use category_list from loaded data for consistent color mapping
color_map = {cat: color for cat, color in zip(category_list, px.colors.qualitative.Pastel * (len(category_list) // len(px.colors.qualitative.Pastel) + 1))}


# Define unique style for special dates
SPECIAL_DATE_COLOR = 'red'
SPECIAL_DATE_SYMBOL = 'circle'
SPECIAL_DATE_SIZE = 10

# Group data by category to plot each one
for category_name in selected_categories:  # Iterate through selected categories
    group_data = filtered_plot_data[filtered_plot_data['category'] == category_name]
    if group_data.empty:
        continue  # Skip if no data for this category

    # Sort data for line plotting
    group_data = group_data.sort_values(by='date')
    category_color = color_map.get(category_name, 'grey')

    # --- Determine RGBA fill color robustly ---
    try:
        # Get the index from the full category_list for consistent color assignment
        color_index = category_list.index(category_name) % len(px.colors.qualitative.Pastel)
        plotly_color_val = px.colors.qualitative.Pastel[color_index]
        rgb_tuple = plotly.colors.hex_to_rgb(plotly_color_val)
        fill_color_rgba = f'rgba({rgb_tuple[0]}, {rgb_tuple[1]}, {rgb_tuple[2]}, 0.3)'
    except ValueError:  # Handle case where category_name might not be in category_list (shouldn't happen)
        print(f"Warning: Category '{category_name}' not found in master color map. Using default grey.")
        fill_color_rgba = 'rgba(128,128,128,0.3)'
    except Exception as e:
        print(f"Warning: Color conversion failed for '{category_name}'. Using default grey fill. Error: {e}")
        fill_color_rgba = 'rgba(128,128,128,0.3)'

    # Separate historical and forecast data
    hist_data = group_data[group_data['type'] == 'Historical']
    forecast_data = group_data[group_data['type'] == 'Forecast']

    # --- Plot Confidence Intervals (Shaded Area) FIRST ---
    if not forecast_data.empty and 'lower' in forecast_data.columns and 'upper' in forecast_data.columns:
        forecast_data_ci = forecast_data.dropna(subset=['lower', 'upper'])
        if not forecast_data_ci.empty:
            fig.add_trace(go.Scatter(
                x=pd.concat([forecast_data_ci['date'], forecast_data_ci['date'][::-1]]),
                y=pd.concat([forecast_data_ci['upper'], forecast_data_ci['lower'][::-1]]),
                fill='toself',
                fillcolor=fill_color_rgba,
                line=dict(color='rgba(255,255,255,0)'),
                hoverinfo="skip",
                showlegend=False,
                legendgroup=category_name,
                name=f'{category_name} CI'
            ))

    # --- Create the "bridge" connecting historical to forecast data ---
    if not hist_data.empty and not forecast_data.empty:
        # Ensure both are sorted by date before getting min/max
        hist_data = hist_data.sort_values('date')
        forecast_data = forecast_data.sort_values('date')

        last_hist_date = hist_data['date'].max()
        last_hist_row = hist_data[hist_data['date'] == last_hist_date].iloc[0]

        first_forecast_date = forecast_data['date'].min()
        first_forecast_row = forecast_data[forecast_data['date'] == first_forecast_date].iloc[0]

        # Create a connecting line segment
        bridge_x = [last_hist_date, first_forecast_date]
        bridge_y = [last_hist_row['revenue'], first_forecast_row['revenue']]

        fig.add_trace(go.Scatter(
            x=bridge_x,
            y=bridge_y,
            mode='lines',
            line=dict(color=category_color, dash='dot'),
            showlegend=False,
            legendgroup=category_name,
            hoverinfo='skip'
        ))

    # Plot Historical Data (Solid Line)
    if not hist_data.empty:
        fig.add_trace(go.Scatter(
            x=hist_data['date'],
            y=hist_data['revenue'],
            mode='lines+markers',  # Add markers for better visibility of points
            name=category_name,
            legendgroup=category_name,
            showlegend=True,  # Show only one legend item per group
            line=dict(dash='solid', color=category_color),
            marker=dict(size=4, color=category_color),
             hovertemplate=f'<b>{category_name}</b><br>Date: %{{x|%m/%d/%Y}}<br>Revenue: %{{y:,.0f}}€<extra></extra>'
        ))

    # Plot Forecast Data (Dashed Line)
    if not forecast_data.empty:
        fig.add_trace(go.Scatter(
            x=forecast_data['date'],
            y=forecast_data['revenue'],
            mode='lines+markers',  # Add markers
            name=f'{category_name} (Forecast)',
            legendgroup=category_name,
            showlegend=False,  # Hide duplicate legend item
            line=dict(dash='dash', color=category_color),
            marker=dict(size=4, color=category_color, symbol='circle-open'),  # Different marker for forecast
            hovertemplate=f'<b>{category_name} (Forecast)</b><br>Date: %{{x|%m/%d/%Y}}<br>Revenue: %{{y:,.0f}}€<extra></extra>'
        ))

    # Plot Special Dates (Markers)
    special_dates_data = group_data.dropna(subset=['special_date'])
    if not special_dates_data.empty:
        fig.add_trace(go.Scatter(
            x=special_dates_data['date'],
            y=special_dates_data['revenue'],
            mode='markers',
            name='Special Date',
            legendgroup=category_name,  # Assign to category group
            showlegend=False,  # Hide from legend for clarity
            marker=dict(
                color=SPECIAL_DATE_COLOR,
                symbol=SPECIAL_DATE_SYMBOL,
                size=SPECIAL_DATE_SIZE,
                line=dict(width=1, color='DarkSlateGrey')
            ),
            customdata=special_dates_data['special_date'],
            hovertemplate='<b>Special Event</b><br>Date: %{x|%m/%d/%Y}<br>Revenue: %{y:,.0f}€<br>Event: %{customdata}<extra></extra>'
        ))


# Update layout
fig.update_layout(
    xaxis_title="Date",
    yaxis_title="Revenue (€)",
    hovermode="x unified",
    legend_title_text='Groups',
    xaxis=dict(tickformat='%m/%d/%Y'),  # Ensure x-axis format
    yaxis=dict(tickformat=',.0f')  # Format y-axis numbers
)
st.plotly_chart(fig, use_container_width=True)


# --- 3. Detailed Table ---
# Prepare data for the table (only forecast data for selected categories from the selected week)
forecast_table_data = filtered_plot_data[
    (filtered_plot_data['type'] == 'Forecast') &
    (filtered_plot_data['category'].isin(selected_categories))
].copy()

# Add a check if forecast table data is empty
if forecast_table_data.empty:
    st.info("No forecast details available for the selected revenue groups in this period.")
else:
    # Select and rename columns for the table
    forecast_table_data['contribution_pct'] = (forecast_table_data['contribution'].fillna(0) * 100).round(0).astype(int)
    forecast_table_data = forecast_table_data[['date', 'category', 'revenue', 'contribution_pct', 'lower', 'upper']]
    forecast_table_data.rename(columns={
        'date': 'Date',
        'category': 'Category',
        'revenue': 'Forecasted Revenue',
        'lower': 'Lower Bound',
        'upper': 'Upper Bound',
        'contribution_pct': 'Contribution (%)'
        }, inplace=True)

    # Pivot table for better readability: Dates as rows, Categories as columns
    try:
        # Ensure 'Date' is in datetime format for proper sorting
        forecast_table_data['Date'] = pd.to_datetime(forecast_table_data['Date'])
        # Combine relevant info into one cell
        forecast_table_data['Details'] = forecast_table_data.apply(
            lambda row: f"{row['Forecasted Revenue']:,.0f} ({row['Contribution (%)']}%)",
            axis=1
        )
        # Pivot
        pivot_table = forecast_table_data.pivot(index='Date', columns='Category', values='Details')
        pivot_table = pivot_table.sort_index()
        pivot_table.index = pivot_table.index.strftime('%m/%d/%Y')  # Format date index

        # Prepare data for download before displaying the table
        download_df = forecast_table_data[['Date', 'Category', 'Forecasted Revenue', 'Contribution (%)', 'Lower Bound', 'Upper Bound']].copy()
        # Format date for CSV
        download_df['Date'] = download_df['Date'].dt.strftime('%m/%d/%Y')
        download_df.sort_values(by=['Date', 'Category'], inplace=True)  # Sort for consistency

        # Convert DataFrame to CSV string
        csv_buffer = StringIO()
        download_df.to_csv(csv_buffer, index=False, encoding='utf-8')
        csv_string = csv_buffer.getvalue()

        # Sanitize filename
        safe_week_key = "".join(c if c.isalnum() else "_" for c in selected_week_key)
        download_filename = f"forecast_details_{safe_week_key}.csv"

        # Add custom CSS for button spacing
        st.markdown("""
        <style>
            .header-flex {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .header-title {
                flex: 3;
            }
            .header-status {
                flex: 1;
                text-align: right;
                font-size: 0.8em;
                color: #666;
            }
            .header-buttons {
                display: flex;
                gap: 5px;
                align-items: center;
            }
            .stButton > button {
                margin: 0;
                padding: 0.25rem 0.5rem;
            }
        </style>
        """, unsafe_allow_html=True)

        # Add status information
        if 'last_run' not in st.session_state:
            st.session_state['last_run'] = datetime.now()
        st.session_state['last_run'] = datetime.now()
        next_run_dt = st.session_state['last_run'] + timedelta(weeks=1)
        next_run = next_run_dt.strftime('%Y-%m-%d %H:%M:%S')

        # Create the header with all elements
        header_container = st.container()
        with header_container:
            col1, col2, col3 = st.columns([3, 1, 1])
            with col1:
                st.markdown(f"<h1>Revenue plans for {selected_week_key}</h1>", unsafe_allow_html=True)
            with col2:
                st.markdown(f"""
                    <div class="header-status">
                        <div>Last run: {st.session_state['last_run'].strftime('%Y-%m-%d %H:%M:%S')}</div>
                        <div>Next run: {next_run}</div>
                    </div>
                """, unsafe_allow_html=True)
            with col3:
                btn_col1, btn_col2, btn_col3 = st.columns([1, 1, 1])
                with btn_col1:
                    if st.button("📥 Download", help="Download the forecast data as a CSV file"):
                        st.download_button(
                            label="Download CSV",
                            data=csv_string,
                            file_name=download_filename,
                            mime="text/csv"
                        )
                with btn_col2:
                    if st.button("🔄 Rerun", help="Rerun the forecast with updated data"):
                        st.session_state['show_rerun_modal'] = True
                with btn_col3:
                    if st.button("📤 Upload", help="Upload a new revenue plan file"):
                        st.session_state['show_upload'] = True

        # Show upload dialog if needed
        if st.session_state.get('show_upload', False):
            st.info("Upload a new revenue plan file")
            uploaded_file = st.file_uploader("Choose a JSON file", type="json", key="revenue_uploader")

            # Add cancel button
            col_upload_cancel, _ = st.columns([1, 3])
            if col_upload_cancel.button("❌ Cancel Upload", key="cancel_upload_button"):
                st.session_state['show_upload'] = False
                st.rerun()

            if uploaded_file is not None:
                try:
                    # Read the uploaded file
                    data = json.load(uploaded_file)
                    # TODO: Add validation and processing of the uploaded file
                    st.success("File uploaded successfully!")
                    st.session_state['show_upload'] = False
                    st.rerun()
                except json.JSONDecodeError:
                    st.error("Invalid JSON file. Please upload a valid JSON file.")
                except Exception as e:
                    st.error(f"Error processing file: {str(e)}")

        # Show confirmation dialog if needed
        if st.session_state.get('show_rerun_modal', False):
            st.warning("Have you uploaded a new revenue plan? If not, the results will be the same as before.")
            col_confirm, col_cancel = st.columns(2)
            if col_confirm.button("Yes, rerun", key="confirm_rerun_button"):
                st.session_state['show_rerun_modal'] = False
                st.rerun()
            if col_cancel.button("Cancel", key="cancel_rerun_button"):
                st.session_state['show_rerun_modal'] = False

        # Display the table
        st.dataframe(pivot_table.fillna('-'), use_container_width=True)

    except Exception as e:
        st.error(f"Could not create the pivot table. Displaying raw forecast data instead. Error: {e}")
        # Fallback table code
        fallback_df = forecast_table_data[['Date', 'Category', 'Forecasted Revenue', 'Contribution (%)', 'Lower Bound', 'Upper Bound']].copy()
        # Format date for fallback table
        fallback_df['Date'] = fallback_df['Date'].dt.strftime('%m/%d/%Y')
        st.dataframe(fallback_df.fillna('-'), use_container_width=True)


# --- Add any other sections as needed ---

