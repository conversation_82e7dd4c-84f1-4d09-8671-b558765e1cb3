# from app.middleware.cors import add_cors_middleware
import contextvars
import json
import os
from typing import Any

from dotenv import load_dotenv
from fastapi import FastAP<PERSON>
from mangum import Mangum

from app import project, router_setup
from app.endpoints import router_v1
from app.logger import logger
from app.middleware.compression import add_compression_middleware
from app.middleware.request_info import add_request_info_middleware

# Define context variable for the logs
request_context = contextvars.ContextVar('request_context', default=None)

project.set_environmental_variables()

app = FastAPI(**project.get_fastapi_config())
# add_cors_middleware(app) # If you uncomment, ensure sane settings in the middleware!
add_compression_middleware(app)
add_request_info_middleware(app)

# Set up auth BEFORE including the router
router_setup.setup_router_dependencies(router_v1)

# Register routers
app.include_router(router_v1, prefix="/api")

mangum_handler = Mangum(app)


def lambda_handler(event: dict[str, Any], context: Any) -> Any:
    # Reset context
    request_context.set({"aws_request_id": context.aws_request_id})  # type: ignore

    # For S3 events, verify the source before processing
    if event.get("Records", []) and event["Records"][0].get("eventSource") == "aws:s3":
        s3_event = event["Records"][0]["s3"]
        bucket_name = s3_event["bucket"]["name"]
        file_path = s3_event["object"]["key"]

        # Update context with S3 event details while preserving existing values
        current_context = request_context.get({})
        current_context['endpoint_path'] = f"lambda_handler s3_event, bucket: {bucket_name}, file_path: {file_path}"
        request_context.set(current_context)

        message = f"Processing file: Bucket: {bucket_name}, File Path: {file_path}"
        logger.info(message)
        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": message,
                }
            ),
        }
    if event.get("Records", []) and event["Records"][0].get("eventSource") == "aws:sqs":
        # Update context with SQS event details while preserving existing values
        current_context = request_context.get({})
        current_context['endpoint_path'] = "lambda_handler sqs_event"
        request_context.set(current_context)

        message = f"Processing SQS event"
        logger.info(message)
        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": message,
                }
            ),
        }

    # If not an S3 or SQS event, handle it as an HTTP request
    return mangum_handler(event, context)


if __name__ == "__main__":
    import uvicorn

    load_dotenv()
    port = int(os.getenv("PORT"))
    uvicorn.run("app.main:app", host="127.0.0.1", port=port, reload=True)
