### Locals ###

locals {
  ### Platform-provided ###
  pl_env                              = "prod"
  pl_env_acc_id                       = data.aws_caller_identity.this.account_id
  pl_region                           = "us-east-1"
  pl_service_revenue_plan_forecasting = "revenue-plan-forecasting"
  pl_iam_role_name                    = "baresquare_service_misc_role"

  # Networking
  dns_zone_name = "${local.pl_env}.baresquare.com"

  # IAM
  iam_allow_ssm_full_access = [
    "ssm:GetParameters",
    "ssm:GetParameter",
    "ssm:GetParametersByPath",
    "ssm:GetParameterHistory",
    "ssm:PutParameter",
    "ssm:DeleteParameter",
    "ssm:DeleteParameters"
  ]
  iam_allow_logging = { actions = ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], resources = ["arn:aws:logs:*:*:*"] }
  iam_allow_s3_full_access = [
    "s3:AbortMultipartUpload",
    "s3:Delete*",
    "s3:Get*",
    "s3:List*",
    "s3:Put*",
    "s3:RestoreObject"
  ]
}