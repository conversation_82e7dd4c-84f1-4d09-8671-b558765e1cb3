module "revenue_plan_forecasting_ecr_repo" {
  source = "**************:BareSquare/terraform-modules//aws/ecr?ref=v3.22.2"

  repo_name        = local.pl_service_revenue_plan_forecasting
  pl_env_acc_id    = local.pl_env_acc_id
  pl_region        = local.pl_region
  pl_iam_role_name = local.pl_iam_role_name

  pl_tags = { "service" = local.pl_service_revenue_plan_forecasting }
}

module "revenue_plan_forecasting_lambda" {
  source = "**************:BareSquare/terraform-modules//aws/lambda?ref=v3.22.2"

  main_iam_role_policy_name = format("%s_%s", local.pl_service_revenue_plan_forecasting, local.pl_region)
  main_iam_role_name        = format("%s_%s", local.pl_service_revenue_plan_forecasting, local.pl_region)
  iam_permissions_boundary  = "arn:aws:iam::${local.pl_env_acc_id}:policy/baresquare_service_misc_permissions_boundary_policy"
  timeout                   = 600
  memory_size               = 384
  ephemeral_storage_size    = 512
  lambda_function_name      = local.pl_service_revenue_plan_forecasting
  maximum_retry_attempts    = 0
  ecr_repo                  = local.pl_service_revenue_plan_forecasting
  image_tag                 = "latest"
  logs_forwarder_lambda_arn = data.terraform_remote_state.platform_env.outputs.logs_forwarder_lambda_arn

  iam_role_policy_attachments = [data.aws_iam_policy.LambdaVPCExecution.arn]
  vpc_id                      = data.terraform_remote_state.platform_env.outputs.vpc_id
  subnet_ids                  = data.terraform_remote_state.platform_env.outputs.private_dmz_subnets

  iam_policy_access = [
    local.iam_allow_logging,
    {
      actions = local.iam_allow_ssm_full_access
      resources = [
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/${local.pl_service_revenue_plan_forecasting}/*",
      ]
    },
    {
      actions = ["ssm:Get*"]
      resources = [
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/auth0/api/audiences",
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/auth0/domain",
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/revenue-plan-forecasting/*",
      ]
    },
    {
      actions = local.iam_allow_s3_full_access
      resources = [
        module.revenue_plan_forecasting_s3.bucket_arn,
        "${module.revenue_plan_forecasting_s3.bucket_arn}/*",
        "arn:aws:s3:::baresquare.${local.pl_region}.${local.pl_env}.mlflow",
        "arn:aws:s3:::baresquare.${local.pl_region}.${local.pl_env}.mlflow/*",
      ]
    },
  ]

  has_url = true
  cors = {
    // CORS is handled by the application itself
  }

  environment_variables = {
    "PL_REGION"           = local.pl_region
    "PL_ENV"              = local.pl_env
    "PL_ENV_ACC_ID"       = local.pl_env_acc_id
    "PL_SERVICE"          = local.pl_service_revenue_plan_forecasting
    "STORAGE_BUCKET_NAME" = module.revenue_plan_forecasting_s3.bucket_name
    "URL_INSIGHTS_APP_BE" = "https://insights-app-be.baresquare.com"
  }

  pl_tags = { "service" = local.pl_service_revenue_plan_forecasting }
}

module "revenue_plan_forecasting_s3" {
  source = "**************:BareSquare/terraform-modules//aws/storage?ref=v3.19.2"

  bucket_name             = "baresquare.${local.pl_region}.${local.pl_env}.${local.pl_service_revenue_plan_forecasting}"
  force_destroy           = false
  versioning_enable       = "Enabled"
  block_public_policy     = true
  block_public_acls       = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  pl_tags = { "service" = local.pl_service_revenue_plan_forecasting }
}

module "revenue_plan_forecasting_cdn" {
  source = "**************:BareSquare/terraform-modules//aws/cloudfront?ref=v3.22.2"

  description            = format(local.pl_service_revenue_plan_forecasting)
  alternate_domain_names = [local.standard_dns_revenue_plan_forecasting, local.friendly_dns_revenue_plan_forecasting]
  origin                 = local.lambda_function_dns_name_revenue_plan_forecasting
  origin_id              = format("Lambda-%s", local.pl_service_revenue_plan_forecasting)

  default_cache_behavior = {
    target_origin_id = format("Lambda-%s", local.pl_service_revenue_plan_forecasting)

    allowed_methods          = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    compress                 = true
    cache_policy_id          = data.aws_cloudfront_cache_policy.cache_policy_id_managed_caching_disabled.id
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.cache_origin_request_policy_id_all_viewer_except_host_header.id
    use_forwarded_values     = false
  }

  viewer_certificate = { acm_certificate_arn = data.terraform_remote_state.platform_env.outputs.cert_arn }

  pl_tags = { "service" = local.pl_service_revenue_plan_forecasting }
}

module "revenue_plan_forecasting_dns_record" {
  source = "**************:BareSquare/terraform-modules//aws/dns?ref=v3.22.2"

  zone_name = local.dns_zone_name
  records = [
    {
      name = local.standard_dns_record_name_revenue_plan_forecasting
      type = "A"
      alias = {
        name                   = module.revenue_plan_forecasting_cdn.dns
        zone_id                = module.revenue_plan_forecasting_cdn.zone_id
        evaluate_target_health = false
      }
    },
    {
      name = local.friendly_dns_record_name_revenue_plan_forecasting
      type = "A"
      alias = {
        name                   = module.revenue_plan_forecasting_cdn.dns
        zone_id                = module.revenue_plan_forecasting_cdn.zone_id
        evaluate_target_health = false
      }
    }
  ]
}

locals {
  lambda_function_dns_name_revenue_plan_forecasting = "${module.revenue_plan_forecasting_lambda.url_id}.lambda-url.${local.pl_region}.on.aws"
  standard_dns_record_name_revenue_plan_forecasting = format("%s.%s", local.pl_service_revenue_plan_forecasting, local.pl_region)
  standard_dns_revenue_plan_forecasting             = format("%s.%s", local.standard_dns_record_name_revenue_plan_forecasting, local.dns_zone_name)
  friendly_dns_record_name_revenue_plan_forecasting = format("%s", local.pl_service_revenue_plan_forecasting)
  friendly_dns_revenue_plan_forecasting             = format("%s.%s", local.friendly_dns_record_name_revenue_plan_forecasting, local.dns_zone_name)

}