terraform {
  required_version = "1.9.6"

  backend "s3" {
    bucket         = "baresquare.us-east-1.test.terraform-remote-state"
    key            = "baresquare.us-east-1.test.remote-state-file.revenue-plan-forecasting.json"
    region         = "us-east-1"
    dynamodb_table = "baresquare.us-east-1.test.terraform-state-locks"
    role_arn       = "arn:aws:iam::720919091850:role/baresquare_service_misc_role"
    encrypt        = true
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.88.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "2.4.2"
    }
  }
}

provider "aws" {
  region = local.pl_region

  default_tags {
    tags = {
      env       = local.pl_env
      region    = local.pl_region
      managedby = "terraform"
    }
  }

  assume_role {
    role_arn = "arn:aws:iam::720919091850:role/baresquare_service_misc_role"
  }
}
