Transform: 'AWS::Serverless-2016-10-31'
Description: An AWS Serverless Specification template describing the function.
Parameters:
  DockerTag:
    Type: String
    Default: latest
    Description: Docker tag to build and deploy.
Resources:
  RevenuePlanForecasting:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: ["app.main.lambda_handler"]
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: .
      DockerTag: !Ref DockerTag
      DockerBuildTarget: lambda_build
      DockerBuildArgs:
        CIRCLE_SHA1: !Ref BuildSha
        CIRCLE_TS: !Ref BuildTs
        GIT_TAG_DESCRIPTIVE: !Ref GitTagDescriptive
