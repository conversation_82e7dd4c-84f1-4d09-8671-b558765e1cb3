version: '3.4'
services:
  revenue-plan-forecasting:
    container_name: rpf
    build:
      context: .
      target: ${DOCKER_BUILD}
      dockerfile: Dockerfile
      args:
        - CIRCLE_SHA1="sha-not-available"
        - CIRCLE_TS="timestamp-not-available"
        - GIT_TAG_DESCRIPTIVE="git-tag-not-available"
    ports:
      - '${PORT}:${DOCKER_PORT}'
    environment:
      - PL_ENV=${PL_ENV}
      - PL_REGION=${PL_REGION}
      - PL_SERVICE=${PL_SERVICE}
      - AWS_PROFILE=bsq-test-us
    volumes:
      - ~/.aws:/root/.aws:ro
      - .:/app

  base-local:
    build:
      context: .
      target: local_build
      dockerfile: Dockerfile
    environment:
      - PL_ENV=dev
      - PORT=${PORT}

  tests:
    extends:
      service: base-local
    container_name: rpf-tests
    command: [ "pytest", "-vv", "--junitxml=test-results/junit.xml" ]

  linting:
    extends:
      service: base-local
    container_name: tpf-linting
    command: [ "ruff", "check", ".", "--preview" ]