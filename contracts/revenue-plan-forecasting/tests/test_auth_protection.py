from fastapi import testclient

from app.main import app

client = testclient.TestClient(app)


def test_auth_protection():
    """Test that all non-public endpoints require authentication."""
    # Get all routes from router_v1
    from app.endpoints import router_v1

    for route in router_v1.routes:
        path = route.path
        method = list(route.methods)[0].lower()  # Get the first HTTP method (e.g., 'get', 'post')
        is_public = getattr(route.endpoint, "is_public", False)

        # Create the full URL path
        full_path = f"/api{path}"

        # Get the request method from the client
        request_method = getattr(client, method)

        # Make the request without auth headers
        response = request_method(full_path)

        if is_public:
            # Public routes should not return 401 or 403
            assert response.status_code not in (401, 403), f"Public route {full_path} requires auth"
        else:
            # Non-public routes should return 401 or 403 without auth
            assert response.status_code in (401, 403), f"Non-public route {full_path} does not require auth"
