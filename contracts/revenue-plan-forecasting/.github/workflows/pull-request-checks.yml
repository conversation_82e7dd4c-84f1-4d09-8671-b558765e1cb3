name: "Semantic Pull Request and automatic labeling"

on:
  pull_request:
    types: [opened, synchronize, reopened, edited, ready_for_review]

jobs:
  main:
    name: Validate PR title and label the PR
    runs-on: ubuntu-22.04
    steps:
      - name: Validate PR title
        uses: BareSquare/action-semantic-pull-request@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # https://github.com/commitizen/conventional-commit-types/blob/master/index.json
          types: |
            [feat]
            [fix]
            [docs]
            [style]
            [refactor]
            [perf]
            [test]
            [build]
            [ci]
            [chore]
            [revert]
          scopes: |
            PL-\d+
            DIAG-\d+
            ADHOC
          requireScope: true
          subjectPattern: ^[A-Z].*$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            starts with an uppercase character.
          headerPattern: '^(\[\w*\]) (?:([\w\$\.\-\* ]*))?\: (.*)$'
          headerPatternCorrespondence: type, scope, subject

      - name: Label the PR
        uses: BareSquare/pr-prefix-labeler@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
