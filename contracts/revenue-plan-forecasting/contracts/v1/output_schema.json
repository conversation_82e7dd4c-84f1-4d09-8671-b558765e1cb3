{"$defs": {"ForecastData": {"properties": {"date": {"format": "date", "title": "Date", "type": "string"}, "revenue": {"title": "Revenue", "type": "integer"}, "lower": {"title": "Lower", "type": "integer"}, "upper": {"title": "Upper", "type": "integer"}, "contribution": {"maximum": 1, "minimum": 0, "title": "Contribution", "type": "number"}, "special_date": {"title": "Special Date", "type": "string"}}, "required": ["date", "revenue", "lower", "upper", "contribution"], "title": "ForecastData", "type": "object"}, "HistoricalData": {"properties": {"date": {"format": "date", "title": "Date", "type": "string"}, "revenue": {"title": "Revenue", "type": "integer"}, "special_date": {"title": "Special Date", "type": "string"}}, "required": ["date", "revenue"], "title": "HistoricalData", "type": "object"}, "KeyMetric": {"properties": {"id": {"title": "Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "symbol": {"title": "Symbol", "type": "string"}}, "required": ["id", "name", "type", "currency", "symbol"], "title": "KeyMetric", "type": "object"}, "RevenueGroup": {"properties": {"name": {"title": "Name", "type": "string"}, "historical": {"items": {"$ref": "#/$defs/HistoricalData"}, "title": "Historical", "type": "array"}, "forecast": {"items": {"$ref": "#/$defs/ForecastData"}, "title": "Forecast", "type": "array"}}, "required": ["name", "historical", "forecast"], "title": "RevenueGroup", "type": "object"}}, "properties": {"summary": {"title": "Summary", "type": "string"}, "period_start": {"format": "date", "title": "Period Start", "type": "string"}, "forecast_window": {"enum": ["daily", "weekly", "monthly"], "title": "Forecast Window", "type": "string"}, "run_date": {"format": "date-time", "title": "Run Date", "type": "string"}, "next_run_date": {"format": "date-time", "title": "Next Run Date", "type": "string"}, "execution_id": {"title": "Execution Id", "type": "string"}, "org_id": {"title": "Org Id", "type": "string"}, "key_metric": {"$ref": "#/$defs/KeyMetric"}, "revenue_groups": {"items": {"$ref": "#/$defs/RevenueGroup"}, "title": "Revenue Groups", "type": "array"}}, "required": ["summary", "period_start", "forecast_window", "run_date", "next_run_date", "execution_id", "org_id", "key_metric", "revenue_groups"], "title": "RevenuePlanOutput", "type": "object"}