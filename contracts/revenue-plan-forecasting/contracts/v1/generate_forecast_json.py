import datetime
import json
import random
from typing import Any, Dict, List


def generate_historical_data(start_date: datetime.date, days: int) -> List[Dict[str, Any]]:
    historical_data = []
    current_date = start_date
    for _ in range(days):
        # Generate random revenue between 1000 and 10000 as integer
        revenue = random.randint(1000, 10000)

        # 5% chance of having a special date
        data = {
            "date": current_date.strftime("%Y-%m-%d"),
            "revenue": revenue
        }

        if random.random() < 0.05:
            data["special_date"] = "Holiday" if random.random() < 0.5 else "Promotion"

        historical_data.append(data)
        current_date += datetime.timedelta(days=1)
    return historical_data

def generate_forecast_data(start_date: datetime.date) -> List[Dict[str, Any]]:
    forecast_data = []
    current_date = start_date

    # Generate forecast for one week (7 days)
    for _ in range(7):
        revenue = random.randint(8000, 15000)
        lower = int(revenue * 0.9)
        upper = int(revenue * 1.1)
        contribution = random.uniform(0.1, 0.2)  # Random contribution between 0.1 and 0.2

        data = {
            "date": current_date.strftime("%Y-%m-%d"),
            "revenue": revenue,
            "lower": lower,
            "upper": upper,
            "contribution": round(contribution, 2)
        }

        # 10% chance of having a special date
        if random.random() < 0.1:
            data["special_date"] = "Holiday" if random.random() < 0.5 else "Promotion"

        forecast_data.append(data)
        current_date += datetime.timedelta(days=1)

    # Normalize contributions to sum to 1
    total_contribution = sum(item["contribution"] for item in forecast_data)
    for item in forecast_data:
        item["contribution"] = round(item["contribution"] / total_contribution, 2)

    return forecast_data

def main():
    categories = [
        "Τηλεφωνία",
        "Υπολογιστές & Περιφερειακά",
        "Βιβλία & Κόμικς",
        "Οικιακές Συσκευές",
        "Οικιακές Μικροσυσκευές",
        "Τηλεοράσεις & Ήχος",
        "total"
    ]

    # Calculate start date for historical data (60 days before forecast date)
    forecast_start_date = datetime.date(2025, 4, 21)
    start_date = forecast_start_date - datetime.timedelta(days=60)

    # Generate data for each category
    categories_data = []
    all_historical = []  # Store historical data for all categories
    all_forecast = []    # Store forecast data for all categories

    for category_name in categories[:-1]:  # Exclude 'total' category
        historical = generate_historical_data(start_date, 60)
        forecast = generate_forecast_data(forecast_start_date)

        category_data = {
            "name": category_name,
            "historical": historical,
            "forecast": forecast
        }

        categories_data.append(category_data)
        all_historical.append(historical)
        all_forecast.append(forecast)

    # Calculate total historical data
    total_historical = []
    for day_idx in range(60):
        day_total = {
            "date": all_historical[0][day_idx]["date"],
            "revenue": sum(category[day_idx]["revenue"] for category in all_historical)
        }
        # Check if any category has a special date for this day
        special_dates = [category[day_idx].get("special_date") for category in all_historical if "special_date" in category[day_idx]]
        if special_dates:
            day_total["special_date"] = special_dates[0]  # Take the first special date found
        total_historical.append(day_total)

    # Calculate total forecast data
    total_forecast = []
    for day_idx in range(7):
        day_total = {
            "date": all_forecast[0][day_idx]["date"],
            "revenue": sum(category[day_idx]["revenue"] for category in all_forecast),
            "lower": sum(category[day_idx]["lower"] for category in all_forecast),
            "upper": sum(category[day_idx]["upper"] for category in all_forecast),
            "contribution": all_forecast[0][day_idx]["contribution"]  # Use the same contribution as it's already normalized
        }
        # Check if any category has a special date for this day
        special_dates = [category[day_idx].get("special_date") for category in all_forecast if "special_date" in category[day_idx]]
        if special_dates:
            day_total["special_date"] = special_dates[0]  # Take the first special date found
        total_forecast.append(day_total)

    # Add total category
    categories_data.append({
        "name": "total",
        "historical": total_historical,
        "forecast": total_forecast
    })

    # Create final JSON structure
    result = {
        "summary": "This forecast provides revenue predictions for various product categories for the week of April 21-27, 2025, along with historical data for the previous 60 days. The forecast includes confidence intervals and special date indicators where applicable.",
        "forecast_week": "2025-04-21",
        "categories": categories_data
    }

    # Save to JSON file
    with open("forecast_data.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    main()
