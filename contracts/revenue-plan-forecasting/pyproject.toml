[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "revene-plan-forecasting"
version = "1.0.0"
license = "LicenseRef-Proprietary"
requires-python = ">=3.12,<4.0"
dependencies = [
    "fastapi==0.111.1",
    "mangum==0.17.0",
    "Authlib==1.3.1",
    "boto3==1.34.151",
    "python-dotenv==1.0.1",
    "pyjwt[crypto]==2.9.0",
    "requests==2.32.3",
    "pydantic[email]",
    "gspread>=6.2.0",
    "pandas>=2.2.3",
    "tenacity>=9.1.2",
    "tomli>=2.2.1",
    "prophet>=1.1.6",
    "statsmodels>=0.14.4",
    "xgboost>=3.0.0",
    "mlflow==2.22.0",
    "plotly>=6.0.1",
]

[project.optional-dependencies]
dev = [
    'ipykernel',
    'uvicorn',
]
linting = [
    'ruff==0.11.5',
]
testing = [
    "pytest==8.2.0",
    "httpx==0.27.0",
]

[tool.pyright]
extraPaths = ["__pypackages__/<major.minor>/lib/"]

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.pytest.ini_options]
pythonpath = "app"
norecursedirs = ".git .tox .env .venv .eggs __pycache__ .pytest_cache .aws-sam"
testpaths = "tests"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "asyncio : marks tests as asyncio (deselect with '-m \"not asyncio\"')",
    ]

[tool.ruff]
src = ["."]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

output-format = "grouped"
line-length = 120

[tool.ruff.lint]
# List of rules https://docs.astral.sh/ruff/rules/
select = [
    # "B",     # bugbear
    "E112",  # no-indented-block
    "E2",    # Whitespace problems (missing or unexpected)
    "E402",  # module-import-not-at-top-of-file
    "E71",   # Comparison errors
    "E721",  # type-comparison
    "E722",  # bare-except
    "E9",
    "I001",  # unsorted-imports
    "W291",  # trailing-whitespace
    "W292",  # missing-newline-at-end-of-file
    "W293",  # blank-line-with-whitespace
    #"UP",    # pyupgrade
    # "PT",    # flake8-pytest-style
    # "G",     # flake8-logging-format
    "D201",  # no-blank-line-before-function
    "D202",  # no-blank-line-after-function
    "D212",  # multi-line-summary-first-line
    "D400",  # ends-in-period
    #"D401",  # non-imperative-mood
    "ASYNC", # flake8-async
    "COM",   # flake8-commas
    # "T20",   # flake8-print
    "RET",   # flake8-return  # use for warning
    "SIM",   # flake8-simplify
    # "ARG",   # flake8-unused-arguments  # use for warning / improvements
    # "PL",    # pylint
    # "F821",  # undefined-name
    "F401",  # unused-import
    "F841",  # unused-variable
    "ARG001",  # unused-function-argument
]

ignore = [
    "UP015",   # redundant-open-modes
    "E501",    # line-too-long
    "SIM112",  # uncapitalized-environment-variables
    "RET503",  # implicit-return
    "D407",    # dashed-underline-after-section
    "SIM113",  # enumerate-for-loop
    "SIM108",  # if-else-block-instead-of-if-exp
    "SIM110",  # reimplemented-builtin
    "COM812",  # missing-trailing-comma (may cause conflicts with autoformatter)
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
docstring-code-format = true
docstring-code-line-length = "dynamic"
exclude = []
line-ending = "lf"
preview = false
skip-magic-trailing-comma = false
