schema-version: v2.2
dd-service: revenue-plan-forecasting
team: baresquare
application: baresquare
description: "Revenue Plan Forecasting"
tier: "1"
lifecycle: "production"
type: web
languages:
  - python
contacts:
  - type: slack
    contact: https://baresquare.slack.com/archives/C1PJHH8SE
links:
  - name: revenue-plan-forecasting
    type: repo
    provider: github
    url: https://github.com/BareSquare/revenue-plan-forecasting/
  - name: Diagnostics Confluence Space
    type: doc
    provider: Confluence
    url: https://bare-square.atlassian.net/wiki/spaces/DIAG
  - name: Diagnostics JIRA project
    type: other
    url: https://bare-square.atlassian.net/jira/software/c/projects/DIAG/boards/38
    provider: Jira
  - name: Architecture and Software Designs Miro board
    type: other
    url: https://miro.com/app/board/uXjVOHWDMGw=/
    provider: Miro