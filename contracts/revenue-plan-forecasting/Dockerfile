FROM python:3.12-slim AS local_build

WORKDIR /app

# Install Uvicorn for live reload
RUN pip install uvicorn

# Install dependencies
COPY pyproject.toml .
RUN pip3 install --no-cache-dir -e .[testing,dev,linting]

COPY . .

ARG CIRCLE_SHA1=default_value
ARG CIRCLE_TS=default_value
ARG GIT_TAG_DESCRIPTIVE=default_value
ENV BUILD_SHA $CIRCLE_SHA1
ENV BUILD_DATE $CIRCLE_TS
ENV BUILD_TAG $GIT_TAG_DESCRIPTIVE

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]


FROM public.ecr.aws/lambda/python:3.12.2024.06.05.12 AS lambda_build

WORKDIR ${LAMBDA_TASK_ROOT}

# Copy the pyproject.toml file first and install dependencies
COPY pyproject.toml .
RUN pip3 install --no-cache-dir -e .

COPY app ./app

ARG CIRCLE_SHA1=default_value
ARG CIRCLE_TS=default_value
ARG GIT_TAG_DESCRIPTIVE=default_value
ENV BUILD_SHA $CIRCLE_SHA1
ENV BUILD_DATE $CIRCLE_TS
ENV BUILD_TAG $GIT_TAG_DESCRIPTIVE

CMD ["app.main.lambda_handler"]