# Revenue Plan Forecasting

## About

TODO

## API documentation

Where to find the app's documentation:

- OpenAPI: served at `/docs`
- Redoc: served at `/redoc`

## How to Run

### Local run (supports live reload)

1. Ensure you have a virtual environment set up.
1. Ensure dependencies are installed with `pip install -e .[testing,dev]`
1. Ensure you have a `.env`; `PORT` must be `3100`
1. Start the local server with `python -m app.main`
1. Invoke the server
   1. `GET` request: `curl -X GET "http://localhost:3100/api/status"`
   1. `GET` request: `curl -X GET "http://localhost:3100/api/sample-get"` 
   1. `POST` request: `curl -X POST "http://localhost:3100/api/sample-post" -d '{}'`
   1. `GET` request on protected endpoint: `curl "http://localhost:3100/api/verify" --header 'Authorization: Bearer JWT_TOKEN'`
   1. `POST` request: `curl -X POST "http://localhost:3100/api/sample-post-with-validation" -H "Content-Type: application/json" -d '{"username": "john.doe", "email": "<EMAIL>"}'`

### Local run with Docker

#### Similarly to scenario without Docker (supports live reload)

1. Ensure you have a `.env` containing the following values:
   ```shell
   PORT=3100
   DOCKER_BUILD=local_build
   DOCKER_PORT=8080
   ```
1. Start the Docker container with `docker compose up` (possibly with `--build`)
1. Invoke the server
   1. `GET` request: `curl -X GET "http://localhost:3100/api/status"`
   1. `GET` request: `curl -X GET "http://localhost:3100/api/sample-get"`
   1. `POST` request: `curl -X POST "http://localhost:3100/api/sample-post" -d '{}'`
   1. `GET` request on protected endpoint: `curl "http://localhost:3100/api/verify" --header 'Authorization: Bearer JWT_TOKEN'`
   1. `POST` request: `curl -X POST "http://localhost:3100/api/sample-post-with-validation" -H "Content-Type: application/json" -d '{"username": "john.doe", "email": "<EMAIL>"}'`

#### Exact same environment as in TEST env

1. Ensure you have a `.env` containing the following values:
   ```shell
   PORT=3100
   DOCKER_BUILD=lambda_build
   DOCKER_PORT=8080
   ```
1. Start the Docker container with `docker compose up` (possibly with `--build`)
1. Invoke the server in the container with:
   - `GET` request: `curl -XPOST "http://localhost:3100/2015-03-31/functions/function/invocations" -d '{"resource": "/", "path": "/api/status", "httpMethod": "GET", "requestContext": {}}'`
   - `GET` request: `curl -XPOST "http://localhost:3100/2015-03-31/functions/function/invocations" -d '{"resource": "/", "path": "/api/sample-get", "httpMethod": "GET", "requestContext": {}}'`
   - `POST` request: `curl -XPOST "http://localhost:3100/2015-03-31/functions/function/invocations" -d '{ "resource": "/", "path": "/api/sample-post", "httpMethod": "POST", "requestContext": {}, "body": "{\"key\": \"value\"}", "isBase64Encoded": false }'`
   - `GET` request on protected endpoint: `curl -XPOST "http://localhost:3100/2015-03-31/functions/function/invocations" -d '{"resource": "/", "path": "/api/verify", "httpMethod": "GET", "requestContext": {}, "headers": {"Authorization": "Bearer XXX"}}'`
   - `POST` request: `curl -XPOST "http://localhost:3100/2015-03-31/functions/function/invocations" -d '{ "resource": "/", "path": "/api/sample-post-with-validation", "httpMethod": "POST", "requestContext": {}, "headers": {"Content-Type": "application/json"}, "body": "{\"username\": \"john.doe\", \"email\": \"<EMAIL>\"}", "isBase64Encoded": false }'`

### Unit tests

- Without Docker: `python -m pytest` or just `pytest`
- With Docker (recommended):
  ```shell
  docker compose build tests
  docker compose up tests
  ``` 
  
### Linting

- Without Docker:
  - Show linting issues: `ruff check . --preview`
  - Fix linting issues: `ruff . --fix --preview`
- With Docker (recommended):
  ```shell
  docker compose build linting
  docker compose up linting
  ``` 

### Postman

Using Postman is perhaps the simplest way to make a `POST` request to our Lambda. The screenshots below show how to use
Postman to do both a `GET` and a `POST` request.

![Postman GET](docs/images/postman-get.png)

![Postman POST](/docs/images/postman-post.png)

### AWS Console

Invoking from the AWS Console requires a complicated payload, because there's no other way for the Lambda to know if we
are making a `GET` or a `POST` request; we need to use the same payloads described in the section on
[how to run as a Lambda build in Docker](#exact-same-environment-as-in-test-env):

```json
{
  "resource": "/",
  "path": "/api/modelling/train",
  "httpMethod": "GET",
  "requestContext": {}
}
```

```json
{
  "resource": "/",
  "path": "/",
  "httpMethod": "POST",
  "requestContext": {},
  "body": "{\"key\": \"value\"}",
  "isBase64Encoded": false
}
```
