version: 2.1

setup: true

orbs:
  path-filtering: circleci/path-filtering@1.1
  continuation: circleci/continuation@1.0

parameters:
  # Releases
  trigger_prod_release:
    type: boolean
    default: false
  release_target_sha:
    type: string
    default: ''
  tag_suffix:
    type: string
    default: ''

workflows:
  version: 2
  setup:
    unless:
      or: [ << pipeline.parameters.trigger_prod_release >> ]
    jobs:
      - path-filtering/filter:
          name: check-updated-files
          base-revision: main
          config-path: .circleci/continue_config.yml
          mapping: |
            app/.* application true
            .dockerignore application true
            Dockerfile application true
            pyproject.toml application true
            template.yaml application true
            tests/.* application true
            terraform/test/.* tf-test true
            terraform/prod/.* tf-prod true
          filters:
            tags:
              only:
                - /^prod-.*/
  setup-manual:
    when:
      or: [ << pipeline.parameters.trigger_prod_release >> ]
    jobs:
      - continuation/continue:
          name: continue-manual-trigger
          configuration_path: .circleci/continue_config.yml