version: 2.1

orbs:
  tf: baresquare/terraform@1.4
  releases: baresquare/releases@1.12

### PARAMETERS ###
parameters:
  application:
    type: boolean
    default: false
  # Terraform
  tf-test:
    type: boolean
    default: false
  tf-prod:
    type: boolean
    default: false
  # Releases
  trigger_prod_release:
    type: boolean
    default: false
  release_target_sha:
    type: string
    default: ''
  tag_suffix:
    type: string
    default: ''
  slack_notifications:
    type: enum
    enum: [ 'ALL', 'SUCCESS_ONLY', 'FAILURE_ONLY', 'NONE' ]
    default: 'FAILURE_ONLY'
    description: Configure which Slack notifications to send


### WORKFLOWS ###
workflows:
  feature_branches:
    jobs:
      - releases/feature_branch_testing:
          name: 'feature_branch_testing'
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          perform_linting: << pipeline.parameters.application >>
          perform_testing: << pipeline.parameters.application >>
          slack_notification_type: << pipeline.parameters.slack_notifications >>
          service_name: 'revenue-plan-forecasting'
          filters:
            branches:
              ignore: main
            tags:
              ignore: /.*/
  test_env:
    when: << pipeline.parameters.application >>
    jobs:
      - releases/build_tagged:
          name: 'build_test'
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          aws_iam_role: 'baresquare_service_misc_role'
          build_with_sam: true
          service_name: 'revenue-plan-forecasting'
          env: 'TEST'
          git_branch_name: 'main'
          git_tag_name: ''
          build_target: 'lambda_build'
          working_directory: '~/project'
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/

      - releases/deploy_lambda_tagged:
          name: 'deploy_test'
          requires:
            - build_test
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          aws_iam_role: 'baresquare_service_misc_role'
          service_name: 'revenue-plan-forecasting'
          env: 'TEST'
          git_tag_name: ''
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          cloud_service: 'aws-lambda'
          slack_notification_type: << pipeline.parameters.slack_notifications >>
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
  prod_env:
    jobs:
      - releases/build_tagged:
          name: 'build_prod'
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          aws_iam_role: 'baresquare_service_misc_role'
          build_with_sam: true
          service_name: 'revenue-plan-forecasting'
          env: 'PROD'
          git_branch_name: 'main'
          git_tag_name: << pipeline.git.tag >>
          build_target: 'lambda_build'
          working_directory: '~/project'
          filters:
            tags:
              only: /^prod-.*/
            branches:
              ignore: /.*/

      - releases/deploy_lambda_tagged:
          name: 'deploy_prod'
          requires:
            - build_prod
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          aws_iam_role: 'baresquare_service_misc_role'
          service_name: 'revenue-plan-forecasting'
          env: 'PROD'
          git_tag_name: << pipeline.git.tag >>
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          cloud_service: 'aws-lambda'
          slack_notification_type: << pipeline.parameters.slack_notifications >>
          generate_release_notes: true
          tag_prefix_prod: 'prod-v'
          filters:
            tags:
              only: /^prod-.*/
            branches:
              ignore: /.*/
  ### Terraform ###
  terraform-test:
    when: << pipeline.parameters.tf-test >>
    jobs:
      - tf/execute:
          name: aws-us-test
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          tf_path: 'terraform/test'
          env: 'TEST'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            tags:
              ignore: /.*/
  terraform-prod:
    when: << pipeline.parameters.tf-prod >>
    jobs:
      - tf/execute:
          name: aws-us-prod
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          tf_path: 'terraform/prod'
          env: 'PROD'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            tags:
              ignore: /.*/
  ### Releases ###
  release_to_prod:
    when: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - releases/push_release_tag:
          context:
            - baresquare_orb_secrets
            - baresquare_service_misc
          env: 'PROD'
          has_stage_env: false
          release_target_sha: << pipeline.parameters.release_target_sha >>
          tag_prefix_prod: 'prod-v'
          tag_version: 'semver'
          tag_suffix: << pipeline.parameters.tag_suffix >>

  ###
  no-changes:
    when:
      not:
        or:
          - << pipeline.parameters.application >>
          - << pipeline.parameters.tf-test >>
          - << pipeline.parameters.tf-prod >>
          - << pipeline.parameters.trigger_prod_release >>
    jobs:
      - tf/do_nothing:
          name: do-nothing
          filters:
            branches:
              only: main
