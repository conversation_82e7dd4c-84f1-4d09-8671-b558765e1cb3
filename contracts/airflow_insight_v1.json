{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "/schemas/Insight", "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "displayId": {"type": "integer"}, "visible": {"type": "boolean"}, "incidentTimeStamp": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "executionId": {"type": "string"}, "aisExecutionId": {"type": "string"}, "orgId": {"type": "string"}, "granularity": {"type": "string", "enum": ["daily", "weekly", "monthly"]}, "isTotal": {"type": "boolean"}, "isSummary": {"type": "boolean"}, "breakdownDimension": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}}, "required": ["key", "value"]}, "funnelMetrics": {"type": "array", "items": {"$ref": "#/components/schemas/Insight/definitions/funnelMetric"}}, "hypotheses": {"type": "array", "items": {"$ref": "#/components/schemas/Insight/definitions/hypothesis"}}, "executiveSummary": {"type": "array", "items": {"type": "string"}}, "overview": {"type": "array", "items": {"type": "string"}}, "keyInsights": {"type": "array", "items": {"type": "string"}}, "actions": {"type": "array", "items": {"type": "string"}}, "visualSummary": {"type": "string"}, "incidentDetails": {"type": "string"}, "rootCauseAnalysisSummary": {"type": "string"}, "rootCauseAnalysisDetails": {"type": "string"}, "aiSuggestions": {"type": "string"}, "aiActions": {"type": "object", "propertyNames": {"enum": ["immediate", "long_term"]}, "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Insight/definitions/aiAction"}}}, "revenueExpectedDeviationPct": {"type": "number"}, "baseline": {"type": "number"}, "revenueDelta": {"type": "number"}, "anomalyDetectionMode": {"type": "string"}, "sensitivity": {"type": "string", "enum": ["low", "medium", "high"]}, "keyMetric": {"$ref": "#/components/schemas/Insight/definitions/keyMetric"}}, "required": ["incidentTimeStamp", "title", "executionId", "id", "aisExecutionId", "orgId", "granularity", "funnelMetrics", "hypotheses", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overview", "keyInsights", "actions", "visualSummary", "incidentDetails", "rootCauseAnalysisSummary", "rootCauseAnalysisDetails", "aiSuggestions", "aiActions", "revenueExpectedDeviationPct", "baseline", "revenueDelta", "anomalyDetectionMode", "sensitivity", "keyMetric", "breakdownDimension"], "definitions": {"funnelMetric": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "baselineValue": {"type": "number"}, "actualValue": {"type": "number"}, "keyMetricImpact": {"type": "number"}, "derived": {"type": "boolean"}}, "required": ["id", "name", "baselineValue", "actualValue", "keyMetricImpact"]}, "hypothesis": {"type": "object", "properties": {"analysis": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "inspector": {"type": "string"}, "result": {"type": "string"}, "result_structured": {"type": ["object", "null", "array", "string"], "additionalProperties": true}, "state": {"type": "string", "enum": ["not-started", "failed", "skipped", "finished"]}, "statement": {"type": "string"}, "verdict": {"type": "string", "enum": ["retained", "rejected", "inconclusive", "skipped", "untested"]}}, "required": ["analysis", "description", "id", "inspector", "result", "state", "statement", "verdict"]}, "aiAction": {"type": "object", "properties": {"id": {"type": "integer"}, "action": {"type": "string"}}, "required": ["id", "action"]}, "keyMetric": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "currency": {"type": "string"}, "symbol": {"type": "string"}, "value": {"type": "number"}, "last_value": {"type": "number"}, "last_year_value": {"type": "number"}, "expected_range": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}}, "required": ["min", "max"]}, "time_series": {"type": "array", "items": {"type": "object", "properties": {"metric": {"type": "number"}, "period": {"type": "string", "format": "date"}}, "required": ["metric", "period"]}}}, "required": ["id", "name", "type", "currency", "symbol", "value", "last_value", "last_year_value", "expected_range", "time_series"]}}}