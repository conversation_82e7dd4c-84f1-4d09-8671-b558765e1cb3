# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Local Development
- `npm run dev` - Start development server with live reload at http://localhost:3003
- `npm run build` - Build TypeScript to dist/ directory
- `npm run lint` - Run ESLint with auto-fix
- `npm run test` - Run all tests with Jest
- `npm run test-some` - Run specific tests with Jest (use `-t` flag)

### Database Migrations
- `npm run migrate create <name>` - Create new migration file
- `DATABASE_URL=<url> npm run migrate up` - Run pending migrations
- `DATABASE_URL=<url> npm run migrate down` - Rollback latest migration

### Docker Development
- `docker compose up` - Start app with PostgreSQL (pgvector) and Redis containers
- `docker compose build linting && docker compose up linting` - Run linting in container
- `docker compose build tests && docker compose up tests` - Run tests in container

## Architecture Overview

### Lambda Handler Architecture
- **Entry Point**: `src/index.ts` - Routes between HTTP (Express) and SQS event processing
- **HTTP API**: `src/http/` - Express.js REST API with Auth0 authentication
- **SQS Consumer**: `src/sqs/` - Processes insights from Airflow SNS topic
- **Shared Code**: `src/common/` - Database, Redis, logging, and AI services

### Key Technologies
- **Node.js 20.11.0+** with TypeScript and ES modules
- **Express.js** with Auth0 JWT authentication
- **PostgreSQL** with pgvector extension for vector embeddings
- **Redis** for caching and session management
- **OpenAI** for embeddings and LLM processing, **Groq** for additional AI
- **Kysely** for type-safe SQL queries
- **LangChain/LangGraph** for AI workflow orchestration

### Search System
The application implements multiple search strategies:

1. **Search v2**: Graph-based semantic search using LangGraph
2. **Search v4**: Refactored text-to-SQL with semantic capabilities
3. **Two-stage retrieval**: SQL query generates candidates (60 results), then LLM filters to final results (20 per page)
4. **Semantic search**: Uses vector embeddings in `revenue_ticket_embedding` table
5. **Text-to-SQL**: Natural language queries converted to SQL using LLM

### Database Schema
- `insights` - Core insights with metadata, hypotheses, summaries
- `revenue_ticket` - Revenue data tickets
- `revenue_ticket_embedding` - Vector embeddings for semantic search
- `user_interactions` - User behavior tracking
- `revenue_forecast_reports` - Revenue forecasting data

## Testing Strategy

### Test Organization
- **Unit tests**: Co-located with source files in `__tests__/` directories
- **Integration tests**: `/test/integration/` directory
- **E2E tests**: `/test/e2e/` directory
- **Fixtures**: `/test/fixtures/` for mock data

### Test Configuration
- Jest with TypeScript support and ES modules
- Testcontainers for PostgreSQL and Redis in integration tests
- Path aliases: `@/*` maps to `src/*`
- Single worker mode due to parallel execution issues
- Global setup/teardown with Docker containers

### Running Tests
- Tests use real PostgreSQL and Redis containers via Testcontainers
- Set `TESTCONTAINERS_RYUK_DISABLED=true` and `TZ=UTC` environment variables
- Groq API calls are mocked in tests via `test/mocks/groq.ts`

## Environment Configuration

### Required Environment Variables
- **Database**: `DB_HOST`, `DB_NAME`, `DB_USERNAME`, `DB_PASSWORD`, `DB_PORT`
- **Redis**: `REDIS_URL`, `REDIS_PASSWORD` (optional for local)
- **AI Services**: `OPENAI_API_KEY`, `GROQ_API_KEY`
- **Auth0**: `AUTH0_DOMAIN`, `BE_APP_AUTH0_AUDIENCE`, `AUTH0_CLAIMS_NAMESPACE`
- **Observability**: `LANGFUSE_PUBLIC_KEY`, `LANGFUSE_SECRET_KEY`, `LANGFUSE_HOST`

### Docker Development
Copy `.env.template` to `.env` and set:
```
DOCKER_BUILD=local_build
DOCKER_PORT=3003
REDIS_URL=redis://redis:6379/3
```

## Code Organization

### Import Path Aliases
TypeScript path mapping: `@/*` resolves to `src/*`

### API Structure
- **Base URL**: `/api/v1/`
- **Health Check**: `/status`
- **Documentation**: `/api/v1/docs` (Swagger)
- **Endpoints**: `/insights`, `/organizations`, `/forecasts`, `/search`

### Logging
- Winston logger with sensitive data redaction
- Structured JSON logging in cloud, formatted locally
- Log levels: debug, info, warn, error
- Sensitive keys automatically redacted from logs

### Submodules
Project includes git submodules. Clone with:
```bash
git clone --recurse-submodules <repo-url>
# OR if already cloned:
git submodule update --init --recursive
```

## Development Notes

### Authentication
- Auth0 JWT Bearer tokens required for API access
- Permission-based access control implemented
- Multiple Auth0 audiences for different services

### AI/LLM Integration
- OpenAI for embeddings and chat completions
- Groq for additional LLM capabilities
- LangChain abstractions for model interactions
- Langfuse for LLM observability and tracing

### Revenue Forecasting
- Specialized forecasting service included as submodule
- Multiple forecasting algorithms (Prophet, SARIMA, XGBoost)
- Time-series analysis and prediction capabilities