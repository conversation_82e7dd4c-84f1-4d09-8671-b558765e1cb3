### Managed policies
data "aws_iam_policy" "LambdaVPCExecution" {
  arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

### CloudFront policies
data "aws_cloudfront_cache_policy" "cache_policy_id_managed_caching_disabled" {
  name = "Managed-CachingDisabled"
}

data "aws_cloudfront_origin_request_policy" "cache_origin_request_policy_id_all_viewer_except_host_header" {
  name = "Managed-AllViewerExceptHostHeader"
}

### Remote state file
data "terraform_remote_state" "platform_env" {
  backend = "s3"
  config = {
    bucket         = "baresquare.${local.pl_region}.${local.pl_env}.terraform-remote-state"
    key            = "baresquare.${local.pl_region}.${local.pl_env}.remote-state-file.platform.json"
    region         = local.pl_region
    dynamodb_table = "baresquare.${local.pl_region}.${local.pl_env}.terraform-state-locks"
    encrypt        = true
    assume_role = {
      role_arn = "arn:aws:iam::${local.pl_env_acc_id}:role/baresquare_service_insights_app_role"
    }
  }
}

### Other ###
data "aws_caller_identity" "this" {}