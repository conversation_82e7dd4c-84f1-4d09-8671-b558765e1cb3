module "insights_app_be_lambda_ecr_repo" {
  source = "**************:BareSquare/terraform-modules//aws/ecr?ref=v3.19.2"

  repo_name        = local.insights_app_be
  pl_env_acc_id    = local.pl_env_acc_id
  pl_region        = local.pl_region
  pl_iam_role_name = local.pl_iam_role_name

  pl_tags = { "service" = local.insights_app_be }
}

module "insights_app_be_lambda" {
  source = "**************:BareSquare/terraform-modules//aws/lambda?ref=v3.19.2"

  main_iam_role_policy_name         = format("%s_%s", local.lambda_insights_app_be, local.pl_region)
  main_iam_role_name                = format("%s_%s", local.lambda_insights_app_be, local.pl_region)
  iam_permissions_boundary          = "arn:aws:iam::${local.pl_env_acc_id}:policy/baresquare_service_insights_app_permissions_boundary_policy"
  timeout                           = 300
  memory_size                       = 384
  lambda_function_name              = local.lambda_insights_app_be
  maximum_retry_attempts            = 0
  provisioned_concurrent_executions = 2
  ecr_repo                          = local.lambda_insights_app_be
  image_tag                         = "latest"
  logs_forwarder_lambda_arn         = data.terraform_remote_state.platform_env.outputs.logs_forwarder_lambda_arn
  batch_size                        = 1
  batch_window                      = 60
  lambda_trigger_source_arn         = [module.insights_app_be_sqs.arn]

  iam_role_policy_attachments = [data.aws_iam_policy.LambdaVPCExecution.arn]
  vpc_id                      = data.terraform_remote_state.platform_env.outputs.vpc_id
  subnet_ids                  = data.terraform_remote_state.platform_env.outputs.private_dmz_subnets

  iam_policy_access = [
    local.iam_allow_logging,
    {
      actions = local.iam_allow_ssm_full_access
      resources = [
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/${local.lambda_insights_app_be}/*",
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/${local.lambda_insights_app_be}",
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/auth0/*",
        "arn:aws:ssm:${local.pl_region}:${local.pl_env_acc_id}:parameter/auth0",
      ]
    },
    {
      actions   = ["sqs:*"]
      resources = [module.insights_app_be_sqs.arn, module.insights_app_be_dlq.arn]
    }
  ]

  has_url = true
  cors = {
    // CORS is handled by the application itself
  }

  environment_variables = {
    "PL_REGION"                       = local.pl_region,
    "PL_ENV"                          = local.pl_env,
    "PL_ENV_ACC_ID"                   = local.pl_env_acc_id,
    "PL_SERVICE"                      = local.lambda_insights_app_be,
    "INSIGHTS_BFF_URL"                = "https://insights-app-bff.baresquare.com",
    "REDIS_URL"                       = "rediss://insights-app-user@${data.terraform_remote_state.platform_env.outputs.elasticache_redis_host}:${data.terraform_remote_state.platform_env.outputs.elasticache_redis_port}/3",
    "INSIGHTS_APP_API_AUTH0_AUDIENCE" = "https://insights-app.prod.baresquare.com"
    "AUTH0_CLAIMS_NAMESPACE"          = "https://baresquare.com",
    "LANGFUSE_PUBLIC_KEY"             = "pk-lf-ded3260e-0a99-47f0-9b2c-e8c20d9f0f65",
    "LANGFUSE_HOST"                   = "https://cloud.langfuse.com"
  }

  pl_tags = { "service" = local.lambda_insights_app_be }
}

module "insights_app_be_cdn" {
  source = "**************:BareSquare/terraform-modules//aws/cloudfront?ref=v3.19.2"

  description            = format(local.pl_service_insights_app_be)
  alternate_domain_names = [local.standard_dns_insights_app_be, local.friendly_dns_insights_app_be]
  origin                 = local.lambda_function_dns_name_insights_app_be
  origin_id              = format("Lambda-%s", local.pl_service_insights_app_be)

  default_cache_behavior = {
    target_origin_id = format("Lambda-%s", local.pl_service_insights_app_be)

    allowed_methods          = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    compress                 = true
    cache_policy_id          = data.aws_cloudfront_cache_policy.cache_policy_id_managed_caching_disabled.id
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.cache_origin_request_policy_id_all_viewer_except_host_header.id
    use_forwarded_values     = false
  }

  viewer_certificate = { acm_certificate_arn = data.terraform_remote_state.platform_env.outputs.cert_arn }

  pl_tags = { "service" = local.pl_service_insights_app_be }
}

module "insights_app_be_dns_record" {
  source = "**************:BareSquare/terraform-modules//aws/dns?ref=v3.19.2"

  zone_name = local.dns_zone_name
  records = [
    {
      name = local.standard_dns_record_name_insights_app_be
      type = "A"
      alias = {
        name                   = module.insights_app_be_cdn.dns
        zone_id                = module.insights_app_be_cdn.zone_id
        evaluate_target_health = false
      }
    }
  ]
}

module "insights_app_be_sqs" {
  source = "**************:BareSquare/terraform-modules//aws/sqs?ref=v3.19.2"

  queue_name                 = local.insights_app_be
  delay_seconds              = local.delay_seconds
  max_message_size           = local.max_message_size
  message_retention_seconds  = 10800
  receive_wait_time_seconds  = 10
  visibility_timeout_seconds = 300
  sse_enabled                = local.encryption_sse_enabled
  sqs_policy_creation        = true
  sns_topic_subscriptions = [
    "arn:aws:sns:us-east-1:528159908831:airflow_ticket_submission"
  ]

  pl_tags = { "service" = local.pl_service_insights_app_be }
}

module "insights_app_be_dlq" {
  source = "**************:BareSquare/terraform-modules//aws/sqs?ref=v3.19.2"

  queue_name                 = "${local.insights_app_be}_dlq"
  delay_seconds              = local.delay_seconds
  max_message_size           = local.max_message_size
  message_retention_seconds  = 14 * 86400
  receive_wait_time_seconds  = 10
  visibility_timeout_seconds = 300
  sse_enabled                = local.encryption_sse_enabled
  dlq_creation               = true
  src_queue_url              = module.insights_app_be_sqs.url
  src_queue_arn              = module.insights_app_be_sqs.arn

  pl_tags = { "service" = local.pl_service_insights_app_be }
}

module "sns_topic_subscriptions_collaboration_ticket_cud_events" {
  source = "**************:BareSquare/terraform-modules//aws/sns?ref=v3.19.2"

  subscriptions = {
    "sub_sns_airflow_ticket_submission" = {
      topic_arn           = "arn:aws:sns:us-east-1:528159908831:airflow_ticket_submission",
      protocol            = "sqs"
      endpoint            = module.insights_app_be_sqs.arn
      filter_policy       = jsonencode({ "targetEnvironment" : [local.pl_env] })
      filter_policy_scope = "MessageAttributes"
    }
  }

  pl_tags = { "service" = local.pl_service_insights_app_be }
}

locals {
  lambda_insights_app_be = "insights_app_be"
  insights_app_be        = "insights_app_be"

  lambda_function_dns_name_insights_app_be = "${module.insights_app_be_lambda.url_id}.lambda-url.${local.pl_region}.on.aws"
  insights_app_be_hostname                 = replace(local.lambda_insights_app_be, "_", "-")
  standard_dns_record_name_insights_app_be = format("%s.%s", local.insights_app_be_hostname, local.pl_region)
  standard_dns_insights_app_be             = format("%s.%s", local.standard_dns_record_name_insights_app_be, local.dns_zone_name)
  friendly_dns_record_name_insights_app_be = format("%s", local.insights_app_be_hostname)
  friendly_dns_insights_app_be             = "${local.insights_app_be_hostname}.baresquare.com"

  delay_seconds          = 0
  max_message_size       = 262144
  encryption_sse_enabled = true
}
