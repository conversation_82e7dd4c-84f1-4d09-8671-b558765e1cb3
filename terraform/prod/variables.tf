locals {
  ### Platform-provided ###
  pl_env                     = "prod"
  pl_env_acc_id              = data.aws_caller_identity.this.account_id
  pl_region                  = "us-east-1"
  pl_service_insights_app_be = "insights_app_be"
  pl_iam_role_name           = "baresquare_service_insights_app_role"

  iam_allow_ssm_full_access = [
    "ssm:GetParameters",
    "ssm:GetParameter",
    "ssm:GetParametersByPath",
    "ssm:GetParameterHistory",
    "ssm:PutParameter",
    "ssm:DeleteParameter",
    "ssm:DeleteParameters"
  ]
  iam_allow_logging = { actions = ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], resources = ["arn:aws:logs:*:*:*"] }
  dns_zone_name     = "${local.pl_env}.baresquare.com"
}