import { Context, APIGatewayProxyEvent, SQSEvent } from 'aws-lambda';
import serverless from 'serverless-http';
import expressApp from './http/index.js';
import { processSqsEvent } from './sqs/index.js';
import logger from './common/utils/logger.js';

const app = async (event: APIGatewayProxyEvent | SQSEvent, context: Context): Promise<unknown> => {
  if ('Records' in event && event.Records[0]?.eventSource === 'aws:sqs') {
    logger.debug('SQS event', { event, context });
    return processSqsEvent(event, context);
  }

  if ('rawPath' in event || 'path' in event) {
    logger.debug('HTTP event', { event, context });
    const serverlessHandler = serverless(expressApp);
    return serverlessHandler(event, context);
  }

  logger.error('Unknown data source', { event, context });
  return false;
};

export default app;
