import { Context, APIGatewayProxyEvent } from 'aws-lambda';
import { fetchSSMParameters } from './common/utils/ssm.js';
import logger from './common/utils/logger.js';
import { SQSEvent } from 'aws-lambda';

let app: (event: APIGatewayProxyEvent | SQSEvent, context: Context) => Promise<unknown>;
let isInitialized = false;

async function initialize() {
  if (isInitialized) {
    return;
  }

  logger.debug('Initializing app...');
  await fetchSSMParameters();

  // Initialize connection pool, see https://github.com/aws-samples/graceful-shutdown-with-aws-lambda
  const pgConn = await import('./common/utils/pgConn.js');
  process.on('SIGTERM', pgConn.gracefullyShutdown);
  logger.debug('Initialized Postgres connection.');

  // Initialize search
  await import('./common/services/search_v4/init.js');
  logger.debug('Initialized search.');

  // Initialize redis client
  const redis = await import('./common/utils/redis.js');
  await redis.client.connect();
  logger.debug('Initialized Redis connection.');

  // Load the app
  const { default: loadedApp } = await import('./index.js');
  app = loadedApp;
  logger.debug('App initialized successfully.');

  isInitialized = true;
}

await initialize();

export const handler = async (event: APIGatewayProxyEvent | SQSEvent, context: Context) => {
  logger.debug('Lambda handler invoked');
  return app(event, context);
};
