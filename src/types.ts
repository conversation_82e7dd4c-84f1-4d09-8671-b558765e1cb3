import { Request } from 'express';
import { AuthResult } from 'express-oauth2-jwt-bearer';

export type DatabaseTables = {
  // Tables
  insights: Record<string, unknown>;
  revenue_ticket: Record<string, unknown>;
  revenue_ticket_embedding: Record<string, unknown>;
  user_interactions: Record<string, unknown>;
  // Views
  user_interactions_latest: Record<string, unknown>;
  user_interactions_latest_summary: Record<string, unknown>;
  // Common Table Expressions (CTEs)
  inserted_ticket: Record<string, unknown>;
  insights_search: Record<string, unknown>;
  insights_search_hypotheses: Record<string, unknown>;
  insights_search_funnel: Record<string, unknown>;
  revenue_forecast_reports: Record<string, unknown>;
};

export type RevenueForecastReport = Record<string, unknown>;

export type Insight = Record<string, unknown>;

export type RevenueTicket = Record<string, unknown>;

export type EmbeddingRecord = Record<string, unknown>;

export type AISuggestionAction = Record<string, unknown>;

export type UserInteraction = Record<string, unknown>;

export type UserInteractionLatestSummary = Record<string, unknown>;

export interface SchemaNode {
  [key: string]:
    | string
    | number
    | boolean
    | SchemaNode
    | (string | number | boolean | SchemaNode)[];
}

export interface ParsedMessage {
  MessageAttributes?: MessageAttributes;
  Message: Record<string, unknown>;
}

export interface MessageAttributes {
  flow?: { Type: string; Value: string };
  version?: { Type: string; Value: string };
  targetEnvironment?: { Type: string; Value: string };
  [key: string]: { Type: string; Value: string } | undefined;
}

// Requests

export interface BaseApiRequest extends Request {
  userId?: string;
  userEmail?: string;
  orgId?: string;
  auth?: AuthResult;
}

export interface AuthenticatedRequest extends BaseApiRequest {
  userId: string;
  userEmail: string;
  orgId: string;
  user: Record<string, unknown>;
  auth: AuthResult;
  userOrgs?: string[];
}

export interface InsightsRequest extends Request {
  orgId?: string;
  userOrgs?: { id: string }[];
}

export interface UserInteractionsRequest extends Request {
  orgId?: string;
  userId?: string;
}

// Search

export interface SearchElements {
  whereClause?: string;
  semanticQuery: string;
  includesProductCategory?: boolean;
}

export interface TicketPool {
  insight_id: string;
  revenue_ticket_id: string;
  text_value: string;
  [key: string]: unknown;
}

export interface SearchResult {
  tickets: TicketPool[];
  nextOffset: number;
}

export interface AgentEvaluationResponse {
  ids: string[];
}

// Other

export interface CustomError extends Error {
  name: string;
  message: string;
}

export type CacheOptions<T> = {
  expiration?: number;
  pred?: (value: T) => boolean;
  maxRetries?: number;
};
