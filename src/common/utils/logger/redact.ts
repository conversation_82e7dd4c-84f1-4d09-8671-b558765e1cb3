import _ from 'lodash';

type SensitiveKey = RegExp;

const makeCustomizer =
  (sensitiveKeys: SensitiveKey[], censor: string) =>
  (_value: unknown, key: string | number | undefined): string | undefined => {
    if (
      key !== undefined &&
      _.isString(key) &&
      sensitiveKeys.some(sensitiveKey => sensitiveKey.test(key))
    ) {
      return censor;
    }
    return undefined;
  };

const makeRedact =
  (sensitiveKeys: SensitiveKey[], censor: string = '[REDACTED]') =>
  (obj: object): object =>
    _.cloneDeepWith(obj, makeCustomizer(sensitiveKeys, censor));

export { makeRedact };
