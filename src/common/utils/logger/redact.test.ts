import { makeRedact } from './redact.js';
import sensitiveKeys from './sensitiveKeys.js';

describe('redact', () => {
  const redact = makeRedact(sensitiveKeys);

  it('should redact nested sensitive keys', () => {
    const obj: Record<string, unknown> = {
      a: '1',
      b: 2,
      authorization: '123',
      Authorization: '123',
      password: '123',
      Password: '123',
      token: '123',
      Token: '123',
      secret: '123',
      Secret: '123',
      c: {
        d: {
          e: {
            authorization: '123',
            Authorization: '123',
            password: '123',
            Password: '123',
            token: '123',
            Token: '123',
            secret: '123',
            Secret: '123',
            _header: '123',
            f: {},
            g: [],
            h: 0,
            i: '',
          },
        },
      },
    };

    const redacted = redact(obj);
    expect(redacted).toEqual({
      a: '1',
      b: 2,
      authorization: '[REDACTED]',
      Authorization: '[REDACTED]',
      password: '[REDACTED]',
      Password: '[REDACTED]',
      token: '[REDACTED]',
      Token: '[REDACTED]',
      secret: '[REDACTED]',
      Secret: '[REDACTED]',
      c: {
        d: {
          e: {
            authorization: '[REDACTED]',
            Authorization: '[REDACTED]',
            password: '[REDACTED]',
            Password: '[REDACTED]',
            token: '[REDACTED]',
            Token: '[REDACTED]',
            secret: '[REDACTED]',
            Secret: '[REDACTED]',
            _header: '[REDACTED]',
            f: {},
            g: [],
            h: 0,
            i: '',
          },
        },
      },
    });
    expect(redacted).not.toBe(obj);
  });
});
