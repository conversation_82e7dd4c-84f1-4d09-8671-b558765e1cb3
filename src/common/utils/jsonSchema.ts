export const convertStringToCamelCase = (str: string) =>
  str.replace(/_([a-z])/g, (_: string, letter: string) => letter.toUpperCase());

/**
 * Mutates a json-schema object, converts property keys from pascal case to camel case
 *
 * @param obj A json-schema object
 * @returns A json-schema object
 */
export const convertPropertiesToCamelCase = (obj: Record<string, unknown>) => {
  if (typeof obj !== 'object' || obj === null) return;

  const properties = obj.properties as Record<string, unknown>;
  const entries = Object.entries(properties);
  entries.forEach(([key, value]) => {
    const camelKey = convertStringToCamelCase(key);
    if (key !== camelKey) {
      delete properties[key];
      properties[camelKey] = value;
    }
  });

  obj.required = ((obj.required as string[]) ?? []).map((s: string) => convertStringToCamelCase(s));
};

export const updateRefs = (obj: Record<string, unknown>) => {
  if (typeof obj !== 'object' || obj === null) return;

  for (const key in obj) {
    if (key === '$ref' && typeof obj[key] === 'string') {
      obj[key] = obj[key].replace('#/$defs/', '#/components/schemas/RevenueForecastReport/$defs/');
    } else {
      updateRefs(obj[key] as Record<string, unknown>);
    }
  }
};

export const addIdentifier = (obj: Record<string, unknown>, key: string, value: unknown) => {
  obj.properties = {
    [key]: value,
    ...(obj.properties as Record<string, unknown>),
  };
  obj.title = 'RevenueForecastReport';
};
