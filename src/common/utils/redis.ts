import { createClient, RedisClientType } from 'redis';
import logger from './logger.js';
import { CacheOptions } from '../../types.js';
import _ from 'lodash';

const config: { url: string; password?: string } = {
  url: process.env.REDIS_URL || '',
};
if (process.env.REDIS_PASSWORD) {
  config.password = process.env.REDIS_PASSWORD;
}

const client: RedisClientType = createClient(config);
logger.debug('Initialized Redis client');

const set = async <T>(key: string, value: T, expiration?: number): Promise<string | null> =>
  client.set(key, JSON.stringify(value), expiration ? { EX: expiration } : {});

const get = async <T>(key: string): Promise<T | null> => {
  const value = await client.get(key);
  return value ? JSON.parse(value) : null;
};

async function withCache<T>(
  key: string,
  fn: () => Promise<T>,
  options?: CacheOptions<T>,
): Promise<T> {
  const cached = await get<T>(key);
  if (!_.isNull(cached)) {
    logger.info(`Cache hit ${key.split(':')[0]}`, { key, value: cached });
    return cached;
  }

  let value: T | undefined;
  let retries = options?.maxRetries ?? 5;
  do {
    try {
      value = await fn();
      break;
    } catch (error) {
      retries--;
      logger.warn(`Retrying cache function, attempts left: ${retries}`, { key, error });
    }
  } while (retries >= 0);

  if (retries < 0 || value === undefined) throw new Error('Max retries exceeded or value not set');

  logger.info('Called withCache', { key, value });
  if (!options?.pred || options?.pred?.(value)) {
    await set(key, value, options?.expiration ?? 0);
  }
  return value;
}

export { client, get, set, withCache };
