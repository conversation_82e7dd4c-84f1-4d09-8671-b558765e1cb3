import { SSMClient, GetParametersByPathCommand } from '@aws-sdk/client-ssm';
import logger from './logger.js';

const ssmClient = new SSMClient({ region: 'us-east-1' });

const parameterPrefixes = ['/auth0', '/insights_app_be'];

const parametersMapping: Record<string, string> = {
  '/auth0/api/audiences': 'AUTH0_AUDIENCES',
  '/auth0/domain': 'AUTH0_DOMAIN',
  '/insights_app_be/db/host': 'DB_HOST',
  '/insights_app_be/db/name': 'DB_NAME',
  '/insights_app_be/db/password': 'DB_PASSWORD',
  '/insights_app_be/db/port': 'DB_PORT',
  '/insights_app_be/db/username': 'DB_USERNAME',
  '/insights_app_be/openai_api_key': 'OPENAI_API_KEY',
  '/insights_app_be/elasticache_redis/password': 'REDIS_PASSWORD',
  '/insights_app_be/langfuse/secret_key': 'LANGFUSE_SECRET_KEY',
  '/insights_app_be/groq_api_key': 'GROQ_API_KEY',
};

async function fetchSSMParameters() {
  try {
    // Fetch all parameters from each prefix path
    const allParameters = [];

    for (const prefix of parameterPrefixes) {
      const command = new GetParametersByPathCommand({
        Path: prefix,
        Recursive: true,
        WithDecryption: true,
      });

      const response = await ssmClient.send(command);
      if (response.Parameters) {
        allParameters.push(...response.Parameters);
      }
    }

    // Set environment variables based on the mapping
    allParameters.forEach(({ Name, Value }) => {
      if (Name && Value && Name in parametersMapping) {
        const key = parametersMapping[Name];
        process.env[key] = Value;
      }
    });

    const audiences = process.env.AUTH0_AUDIENCES;
    if (audiences) {
      process.env.BE_APP_AUTH0_AUDIENCE = JSON.parse(audiences)['insights-app-be'];
    }

    logger.debug('Set SSM parameters to environment variables.');
  } catch (error) {
    logger.error('Error fetching parameters from SSM:', error);
    throw error;
  }
}

export { fetchSSMParameters };
