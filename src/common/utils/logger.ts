import { createLogger, transports, format } from 'winston';
import _ from 'lodash';
import { makeRedact } from './logger/redact.js';
import sensitiveKeys from './logger/sensitiveKeys.js';
import { TransformableInfo } from 'logform';
// See https://github.com/BareSquare/insights-app-bff/pull/139
import * as flatted from 'flatted';

const redact = makeRedact(sensitiveKeys);

const groupCloudMetadata = ({
  level,
  message,
  ...metadata
}: TransformableInfo): TransformableInfo => ({
  level,
  message,
  metadata: _.isEmpty(metadata) ? undefined : flatted.parse(flatted.stringify(metadata)),
});

const cloudFormat = format.combine(
  format(groupCloudMetadata)(),
  format(info => redact(info) as TransformableInfo)(),
  format.json(),
);

const groupLocalMetadata = ({ timestamp, level, message, ...metadata }: TransformableInfo) =>
  ({
    // Use underscore to ensure fields appear first
    _timestamp: timestamp,
    _level: level.toUpperCase(),
    _message: message,
    metadata: _.isEmpty(metadata) ? undefined : flatted.parse(flatted.stringify(metadata)),
    // Double assertion or type assertion chaining: Winston will accept any type (incl. our new underscored fields
    // and removing the level/message fields), but first we need to stop TypeScript from complaining about incompatible
    // types by throwing away type information with "as unknown", then cast to Winston's TransformableInfo.
  }) as unknown as TransformableInfo;

const localFormat = format.combine(
  format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
  }),
  format(groupLocalMetadata)(),
  format(info => redact(info) as TransformableInfo)(), // comment out this line to see the sensitive data locally, but don't commit it
  format.prettyPrint(),
);

const isLocal = _.isUndefined(process.env.PL_ENV);
const logFormat = isLocal ? localFormat : cloudFormat;
const logger = createLogger({
  level: process.env.LOGGER_LEVEL || 'info',
  transports: [
    new transports.Console({
      format: logFormat,
    }),
  ],
});

export default logger;
