import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc.js';

dayjs.extend(utc);

/**
 * Formats a date string to 'MMMM D, YYYY' format. It checks if the date is in UTC format
 * (ends with 'Z'). If not, it attempts to use the provided UTC offset in the date string.
 * @param date - The date string to format.
 * @returns The formatted date string.
 */
function formatDateRespectingTimezone(date: string | Date): string {
  const dateStr = date instanceof Date ? date.toISOString() : date;
  // example UTC Time 2024-04-02T21:50:00.000Z
  const isUTC = dateStr.endsWith('Z');
  // example Tokyo Time (Japan Standard Time, UTC+9)
  // 2024-04-03T06:50:00.000+09:00
  const originalTimezone = dateStr.slice(-6);
  const utcOffset = isUTC ? 0 : originalTimezone;
  return dayjs(dateStr).utcOffset(utcOffset).format('MMMM D, YYYY');
}

export { formatDateRespectingTimezone };
