import { transports, Logger } from 'winston';

describe('logger LOCAL', () => {
  const originalPlEnv = process.env.PL_ENV;
  let output = '';
  let logSpy: jest.SpyInstance;
  // Used to create a fresh logger for every test
  let logger: Logger;

  beforeAll(async () => {
    delete process.env.PL_ENV;
    jest.resetModules();
    const loggerModule = await import('./logger.js');
    logger = loggerModule.default;
    logSpy = jest
      .spyOn(logger.transports[0], 'log')
      .mockImplementation((info, callback?: () => void) => {
        output += JSON.stringify(info) + '\n';
        if (callback) callback();
      });
  });

  afterAll(() => {
    process.env.PL_ENV = originalPlEnv;
    logSpy.mockRestore();
  });

  beforeEach(() => {
    output = '';
  });

  it('should have a Console transport', () => {
    expect(logger.transports).toHaveLength(1);
    expect(logger.transports[0]).toBeInstanceOf(transports.Console);
  });

  it('should log messages in the local format', () => {
    logger.info('Test message');
    expect(JSON.parse(output)).toEqual(
      expect.objectContaining({ _level: 'INFO', _message: 'Test message' }),
    );
  });

  it('should redact secrets in the local format', () => {
    logger.info('Test message', { sensitive: { data: { token: '12345' } } });
    expect(JSON.parse(output)).toEqual(
      expect.objectContaining({
        _level: 'INFO',
        _message: 'Test message',
        metadata: {
          sensitive: {
            data: {
              token: '[REDACTED]',
            },
          },
        },
      }),
    );
  });
});

describe('logger CLOUD', () => {
  const originalPlEnv = process.env.PL_ENV;
  let output = '';
  let logSpy: jest.SpyInstance;
  // Used to create a fresh logger for every test
  let logger: Logger;

  beforeEach(async () => {
    process.env.PL_ENV = 'anything-except-undefined';
    jest.resetModules();
    const loggerModule = await import('./logger.js');
    logger = loggerModule.default;
    output = '';
    logSpy = jest
      .spyOn(logger.transports[0], 'log')
      .mockImplementation((info, callback?: () => void) => {
        output += JSON.stringify(info) + '\n';
        if (callback) callback();
      });
  });

  afterAll(() => {
    process.env.PL_ENV = originalPlEnv;
    logSpy.mockRestore();
  });

  it('should have a Console transport', () => {
    expect(logger.transports).toHaveLength(1);
    expect(logger.transports[0]).toBeInstanceOf(transports.Console);
  });

  it('should log messages in the cloud format', () => {
    logger.info('Test message');
    expect(JSON.parse(output)).toEqual({ level: 'info', message: 'Test message' });
  });

  it('should redact secrets in the cloud format', () => {
    logger.info('Test message', { sensitive: { data: { token: '12345' } } });
    expect(JSON.parse(output)).toEqual({
      level: 'info',
      message: 'Test message',
      metadata: {
        sensitive: {
          data: {
            token: '[REDACTED]',
          },
        },
      },
    });
  });
});
