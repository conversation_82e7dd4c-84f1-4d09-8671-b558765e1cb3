import { transaction, pgPool } from './pgConn.js';

describe('transaction', () => {
  const queryMock = jest.fn();
  const clientReleaseMock = jest.fn();

  beforeEach(() => {
    // Directly mock the `connect` function on the `pgPool` instance
    pgPool.connect = jest.fn().mockResolvedValue({
      query: queryMock,
      release: clientReleaseMock,
    }) as jest.Mock;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should execute queries in a transaction', async () => {
    // run some updates in a transaction
    await transaction([
      "UPDATE revenue_ticket SET currency = 'USD' WHERE insight_id = 55548",
      "UPDATE revenue_ticket SET currency = 'USD' WHERE insight_id = 55547",
    ]);

    // check if the right calls have been made
    expect(queryMock).toHaveBeenCalledTimes(4);
    expect(queryMock).toHaveBeenNthCalledWith(1, 'BEGIN');
    expect(queryMock).toHaveBeenNthCalledWith(
      2,
      "UPDATE revenue_ticket SET currency = 'USD' WHERE insight_id = 55548",
    );
    expect(queryMock).toHaveBeenNthCalledWith(
      3,
      "UPDATE revenue_ticket SET currency = 'USD' WHERE insight_id = 55547",
    );
    expect(queryMock).toHaveBeenNthCalledWith(4, 'COMMIT');
    expect(clientReleaseMock).toHaveBeenCalledTimes(1);
  });

  it('should throw an error if the first update fails', async () => {
    queryMock.mockResolvedValueOnce(true);
    queryMock.mockRejectedValueOnce(new Error('Failed to update'));

    // run some updates in a transaction, the first update failing
    await expect(
      transaction([
        'UPDATE revenue_ticket SET currency = 3 WHERE insight_id = 55548',
        "UPDATE revenue_ticket SET currency = 'USD' WHERE insight_id = 55547",
      ]),
    ).rejects.toThrow();

    // check if the transaction was rolled back
    expect(queryMock).toHaveBeenCalledTimes(3);
    expect(queryMock).toHaveBeenNthCalledWith(1, 'BEGIN');
    expect(queryMock).toHaveBeenNthCalledWith(
      2,
      'UPDATE revenue_ticket SET currency = 3 WHERE insight_id = 55548',
    );
    expect(queryMock).toHaveBeenNthCalledWith(3, 'ROLLBACK');
    expect(clientReleaseMock).toHaveBeenCalledTimes(1);
  });
});
