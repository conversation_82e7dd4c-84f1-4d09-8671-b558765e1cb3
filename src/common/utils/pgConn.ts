import logger from './logger.js';
import { <PERSON>ysely, PostgresDialect, CamelCasePlugin } from 'kysely';
import { DatabaseTables } from '../../types.js';
import pg from 'pg';
import type { Pool as PoolType, ClientBase } from 'pg';
const { Pool, types } = pg;

// see https://www.npmjs.com/package/pg-types
types.setTypeParser(20, (val: string) => parseInt(val, 10));

type DbConfig = {
  user: string | undefined;
  host: string | undefined;
  database: string | undefined;
  password: string | undefined;
  port: number;
};

const config: DbConfig = {
  user: process.env.DB_USERNAME,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT || '5432'),
};

const pgPool = new Pool(config) as unknown as PoolType & ClientBase;
logger.debug('Initialized Postgres connection pool');

const kysely = new Kysely<DatabaseTables>({
  dialect: new PostgresDialect({
    pool: pgPool,
  }),
  plugins: [new CamelCasePlugin({ maintainNestedObjectKeys: true })],
});

async function gracefullyShutdown(): Promise<void> {
  logger.info('[runtime] Termination signal received');
  await pgPool.end();
  logger.info('[runtime] Exiting');
}

// client.query below can handle both type string (an SQL query) and type text-values (parameterised queries)
type QueryInput = string | { text: string; values?: unknown[] };

const transaction = async <T = unknown>(queries: QueryInput[]): Promise<T> => {
  const client = await pgPool.connect();
  try {
    await client.query('BEGIN');

    for (const q of queries) {
      //logger.info('Adding one query to the transaction', { query: q });
      await client.query(q);
    }

    const res = await client.query('COMMIT');
    logger.info('Transaction completed');
    return res as T;
  } catch (e) {
    await client.query('ROLLBACK');
    logger.error('Transaction failed', { error: e });
    throw e;
  } finally {
    client.release();
  }
};

export { pgPool, kysely, gracefullyShutdown, transaction };
