import { Insight } from '../../types.js';
import createInsight from './searchService/queries/createTicket.js';
import { transaction, kysely } from '../utils/pgConn.js';
import logger from '../utils/logger.js';
import * as openAIService from '../services/openAIService.js';
import * as config from './searchService/config.js';

type LegacyFields = Record<string, unknown>;

const legacyFieldNames: LegacyFields = {
  title: 'category',
  executiveSummary: 'lv1ExecutiveSummary',
  keyInsights: 'lv2KeyInsights',
  overview: 'lv2Overview',
  visualSummary: 'lv2VisualSummary',
  incidentDetails: 'lv3IncidentDetails',
  rootCauseAnalysisDetails: 'lv3RootCauseAnalysisDetails',
  rootCauseAnalysisSummary: 'lv3RootCauseAnalysisSummary',
};

const updateEmbedding = async (revenueTicketId: string, field: string, newValue: string) => {
  const { data, usage } = await openAIService.generateEmbedding(newValue);
  const embedding = data[0].embedding;
  const cost = usage.total_tokens;

  logger.info('Updating embedding', { revenueTicketId, field, newValue });

  return kysely
    .updateTable('revenue_ticket_embedding')
    .set({
      text_value: newValue,
      embedding: `[${embedding}]`,
      platform: config.platform,
      model: config.embeddingsModel,
      cost: cost,
      cost_unit: 'tokens',
      model_params: JSON.stringify(config.embeddingsModelParams).replace(/'/g, "''"),
    })
    .where('revenue_ticket_id', '=', revenueTicketId)
    .where('revenue_ticket_field', 'in', [field, legacyFieldNames[field]])
    .execute();
};

const deleteSearchIndex = async (insightId: string) => {
  return kysely.deleteFrom('revenue_ticket').where('insightId', '=', insightId).execute();
};

type ParameterisedQuery = { text: string; values: unknown[] };

const indexInsightsForSearch = async (insights: Insight[]) => {
  const queries = await Promise.allSettled(insights.map(createInsight)).then(results => {
    const fulfilledQueries = results
      .filter(
        (result): result is PromiseFulfilledResult<ParameterisedQuery> =>
          result.status === 'fulfilled',
      )
      .map(result => result.value)
      .flat();

    const failedQueries = results
      .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
      .map(result => {
        if (result.reason instanceof Error) {
          return {
            status: result.status,
            reason: {
              message: result.reason.message,
              stack: result.reason.stack,
            },
          };
        }
        return result;
      });

    if (failedQueries.length) {
      logger.error('Failed to index some insights for search', { failedQueries });
      throw new Error('Failed to index some insights for search');
    }
    return fulfilledQueries;
  });

  logger.info('Queries', { queries });

  logger.info(`Running transaction for ${queries.length} queries...`);
  const transactionRes = await transaction(queries);
  logger.info('Transaction done!');

  return transactionRes;
};

export { indexInsightsForSearch, updateEmbedding, deleteSearchIndex };
