import '../../../test/tools/usePostgres';
import { validateAndStoreInsightsTrx } from './insightsService.js';
import testInsight from '../../../test/resources/airflow/testTicket.json' with { type: 'json' };
import testInsightNullSequence from '../../../test/resources/airflow/testTicketNullSequence.json' with { type: 'json' };
import { kysely } from '../../common/utils/pgConn.js';
import { DatabaseTables, Insight } from '../../types.js';
import { TransactionBuilder } from 'kysely';

const validateAndStoreInsights = async (insights: Insight[]): Promise<void> => {
  const result = await kysely.transaction().execute(async trx => {
    await validateAndStoreInsightsTrx(insights, trx);
  });
  console.log('result', result);
  return result;
};

describe('airflow insight', () => {
  it('should validate and store a valid ticket', async () => {
    await validateAndStoreInsights([testInsight]);

    const insertedInsight = await kysely
      .selectFrom('insights')
      .selectAll()
      .where('id', '=', testInsight.id)
      .executeTakeFirst();

    expect(insertedInsight).toBeTruthy();
    expect(insertedInsight?.id).toEqual(testInsight.id);
  });

  it('should throw an error for an invalid ticket', async () => {
    const invalidTicket = { ...testInsight, id: null } as Insight;
    await expect(validateAndStoreInsights([invalidTicket])).rejects.toThrow(
      'Insight failed to pass validation: data/id must be string',
    );
  });

  it('should throw an error when database insertion fails', async () => {
    const mockExecute = jest.fn().mockRejectedValue(new Error('Database error'));
    jest.spyOn(kysely, 'transaction').mockImplementation(
      () =>
        ({
          execute: mockExecute,
        }) as unknown as TransactionBuilder<DatabaseTables>,
    );

    await expect(validateAndStoreInsights([testInsight])).rejects.toThrow('Database error');

    expect(mockExecute).toHaveBeenCalled();
    jest.restoreAllMocks();
  });

  it('should properly sanitize and store a ticket with NULL byte sequences', async () => {
    const jsonString = JSON.stringify(testInsightNullSequence);
    expect(jsonString).toContain('\\u0000');

    await validateAndStoreInsights([testInsightNullSequence]);

    const insertedInsight = await kysely
      .selectFrom('insights')
      .selectAll()
      .where('id', '=', testInsightNullSequence.id)
      .executeTakeFirst() as Insight;

    expect(insertedInsight).toBeTruthy();

    const storedJson = JSON.stringify(insertedInsight);
    expect(storedJson).not.toContain('\\u0000');

    // Check that the proper character replacements were made
    const typedInsight = insertedInsight as Record<string, unknown> & {
      id: string;
      hypotheses: Array<Record<string, unknown>>;
    };
    const hypothesesWithQuotes = typedInsight.hypotheses.some(h =>
      JSON.stringify(h).includes("'robust'")
    );
    expect(hypothesesWithQuotes).toBeTruthy();

    // Additional verification that the data maintained integrity
    expect(typedInsight.id).toEqual(testInsightNullSequence.id);
    expect(typedInsight.hypotheses.length).toEqual(testInsightNullSequence.hypotheses.length);
  });
});
