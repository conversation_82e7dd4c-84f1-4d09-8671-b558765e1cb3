import { Ajv, ValidateFunction } from 'ajv';
import _addFormats from 'ajv-formats'; // Workaround - see https://github.com/ajv-validator/ajv-formats/issues/85#issuecomment-2262652443
const addFormats = _addFormats as unknown as typeof _addFormats.default;
import { Insight, DatabaseTables, SchemaNode } from '../../types.js';
import { Kysely } from 'kysely';
import SCHEMA from '../../../contracts/airflow_insight_v1.json' with { type: 'json' };
import logger from '../../common/utils/logger.js';

const ajv = new Ajv({
  // Add support for references
  $data: true,
  allowUnionTypes: true,
});
addFormats(ajv);

const schemaDeepCopy = JSON.parse(JSON.stringify(SCHEMA));

// Rewrite references to use local definitions
function rewriteReferences(obj: SchemaNode): SchemaNode {
  if (typeof obj !== 'object' || obj === null) return obj;

  for (const key in obj) {
    const value = obj[key];
    if (value && typeof value === 'object' && '$ref' in value) {
      const refValue = (value as { $ref: string }).$ref;
      if (refValue && refValue.includes('#/components/schemas/Insight/definitions/')) {
        (value as { $ref: string }).$ref = refValue.replace(
          '#/components/schemas/Insight/definitions/',
          '#/definitions/',
        );
      }
    }
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      rewriteReferences(value);
    }
  }
  return obj;
}

function sanitizeJsonObject<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    // Handle the specific \u0000XX pattern
    // eslint-disable-next-line no-control-regex
    let sanitized = obj.replace(/\u0000([0-9a-fA-F]{2})/g, (_match, hexCode) => {
      try {
        const decimalValue = parseInt(hexCode, 16);
        const sanitizedString = String.fromCharCode(decimalValue);
        logger.warn(`Sanitized string ${obj} to ${sanitizedString}`);
        return sanitizedString;
      } catch (error) {
        logger.warn(`Unable to sanitize string ${obj}`, error);
        return hexCode;
      }
    }) as string;

    // Handle the \u0000 pattern
    // eslint-disable-next-line no-control-regex
    sanitized = sanitized.replace(/\u0000/g, '');

    return sanitized as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeJsonObject(item)) as unknown as T;
  }

  if (typeof obj === 'object') {
    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
      result[key] = sanitizeJsonObject(value);
    }
    return result as unknown as T;
  }

  return obj;
}

function validateAndPrepareInsight(insight: Insight, validate: ValidateFunction): Insight {
  const isValid = validate(insight);

  if (!isValid) {
    const errorMessage = `Insight failed to pass validation: ${ajv.errorsText(validate.errors)}`;
    logger.error(errorMessage, { insight, errors: validate.errors });
    throw new Error(errorMessage, { cause: 'malformed-request' } as ErrorOptions);
  }

  // Sanitise insight
  const sanitizedInsight = sanitizeJsonObject(insight);

  // Prepare the insight for database insertion
  return {
    ...sanitizedInsight,
    funnelMetrics: JSON.stringify(sanitizedInsight.funnelMetrics),
    hypotheses: JSON.stringify(sanitizedInsight.hypotheses),
    aiActions: JSON.stringify(sanitizedInsight.aiActions),
    keyMetric: JSON.stringify(sanitizedInsight.keyMetric),
    breakdownDimension: JSON.stringify(sanitizedInsight.breakdownDimension),
  };
}

// Use deep copy of schema because Node.js caches require statements,
// so changing the original SCHEMA variable would impact Swagger docs
rewriteReferences(schemaDeepCopy);
const validate = ajv.compile(schemaDeepCopy);

async function validateAndStoreInsightsTrx(
  insights: Insight[],
  trx: Kysely<DatabaseTables>,
): Promise<Insight[]> {
  try {
    const createdInsights: Insight[] = [];
    for (const insight of insights) {
      const preparedInsight = validateAndPrepareInsight(insight, validate);
      const createdInsight = await trx
        .insertInto('insights')
        .values(preparedInsight)
        .returningAll()
        .execute();

      createdInsights.push(createdInsight[0]);
    }
    return createdInsights;
  } catch (error) {
    const errorMessage = `Failed to insert insights to the DB: ${(error as Error).message}`;
    logger.error(errorMessage, error);
    throw new Error(errorMessage);
  }
}

export { validateAndPrepareInsight, validateAndStoreInsightsTrx };
