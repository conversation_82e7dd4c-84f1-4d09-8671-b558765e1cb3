import { DatabaseTables } from '@/types.js';
import {
  AliasedRawBuilder,
  ComparisonOperator,
  OrderByDirectionExpression,
  SelectQueryBuilder,
  TableExpression,
} from 'kysely';

export type Insight = Record<string, unknown>;

export interface SearchResult {
  insights: Insight[];
  filteredCount: number;
}

export interface SemanticQueryOutput {
  missing: string;
}

export type WhereTuple = [string, ComparisonOperator, string | number | boolean];
export type JoinTuple = [
  TableExpression<DatabaseTables & { i: Record<string, unknown> }, 'i'>,
  string,
  string,
];

export type Partial<T extends string> = {
  [key in T]?: WhereTuple[];
};

export interface Partials {
  WHERE?: WhereTuple[];
  JOIN?: JoinTuple[];
  SELECT?: (AliasedRawBuilder<unknown, 'distance'> | string)[] | undefined;
  DISTINCT_ON?: string[];
  ORDER_BY?: [string, OrderByDirectionExpression];
  LIMIT?: number[];
}

export interface SearchResult {
  insights: Insight[];
  filteredCount: number;
}

export type SearchCompiledQuery = SelectQueryBuilder<
  DatabaseTables & {
    i: Record<string, unknown>;
  },
  'i',
  {
    distance: unknown;
    textValue: unknown;
    revenueTicketId: unknown;
    insightJson: unknown;
    insightId: unknown;
    incidentTimeStamp: unknown;
  }
>;

export type SemanticQuery = {
  text: string;
  embedding: number[];
};
