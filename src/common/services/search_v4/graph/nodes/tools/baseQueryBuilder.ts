import { SearchCompiledQuery } from '../../../types.js';
import { StateAnnotation } from '../../annotations.js';
import { kysely } from '@/common/utils/pgConn.js';

export default async ({
  orgId,
  partials,
  semanticQuery,
}: typeof StateAnnotation.State): Promise<{ baseQuery: SearchCompiledQuery }> => {
  // BASE QUERY
  let query = kysely
    .selectFrom('insights_search as i')
    .where('i.orgId', '=', orgId)
    .select(['i.insightId', 'i.incidentTimeStamp', 'i.insightJson', ...(partials.SELECT || [])]);

  // DYNAMIC PARTS
  for (const join of partials.JOIN || []) {
    // @ts-expect-error - up to the developer to supply correct values
    query = query.innerJoin(...join);
  }
  for (const clause of partials.WHERE || []) query = query.where(...clause);
  if (partials.DISTINCT_ON?.length) query = query.distinctOn(partials.DISTINCT_ON[0]);
  if (semanticQuery) query = query.orderBy('revenueTicketId').orderBy('distance', 'asc');

  return {
    // @ts-expect-error - same as above
    baseQuery: query,
  };
};
