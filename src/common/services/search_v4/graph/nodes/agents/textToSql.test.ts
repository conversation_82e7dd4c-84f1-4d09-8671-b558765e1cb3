import { jest } from '@jest/globals';

// Mock the database connection to avoid database queries
jest.unstable_mockModule('@/common/utils/pgConn.js', () => ({
  kysely: {} as any,
}));

// Mock the init file to avoid database queries - needs to be first
jest.unstable_mockModule('@/common/services/search_v4/init.js', () => ({
  enums: {
    funnel: {
      names: { org123: ['checkout', 'signup', 'purchase'] },
    },
    hypotheses: {
      ids: { org123: ['hyp1', 'hyp2', 'hyp3'] },
      inspectors: { org123: ['inspector1', 'inspector2'] },
    },
  },
}));

// Mock the generateEmbedding function
const mockGenerateEmbedding = jest.fn() as jest.MockedFunction<any>;
jest.unstable_mockModule('@/common/services/openAIService.js', () => ({
  generateEmbedding: mockGenerateEmbedding,
}));

import { mockInvoke } from '@/../test/mocks/groq.js';
import { InputAnnotation } from '../../annotations.js';

// Declare the textToSql function type
let textToSql: (state: typeof InputAnnotation.State, config: { callbacks: any[] }) => Promise<any>;

describe('textToSql', () => {
  beforeAll(async () => {
    // Dynamically import the module AFTER the mocks are set up
    const module = await import('./textToSql.js');
    textToSql = module.default;
  });

  /* reset call‑count & one‑off implementations */
  beforeEach(() => {
    jest.clearAllMocks();
    mockGenerateEmbedding.mockResolvedValue({
      data: [{ embedding: [0.1, 0.2, 0.3] }],
    } as any);
  });

  it('handles basic filtering only', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              basic_filtering: [['incident_time_stamp', '>=', '2025-01-01']],
              explanation: 'Filtered by date',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      {
        userQuery: 'show me tickets from January',
        orgId: 'org123',
      } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.partials.WHERE).toEqual([['i.incident_time_stamp', '>=', '2025-01-01']]);
    expect(result.semanticQuery).toBeUndefined();
    expect(mockInvoke).toHaveBeenCalledTimes(1);
  });

  it('handles semantic search', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              semantic_search: 'payment issues',
              explanation: 'Searching for payment-related problems',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      { userQuery: 'find payment issues', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.semanticQuery).toEqual({
      text: 'payment issues',
      embedding: [0.1, 0.2, 0.3],
    });
    expect(result.partials.JOIN).toEqual([
      ['revenue_ticket as r', 'r.insightId', 'i.insightId'],
      ['revenue_ticket_embedding as e', 'e.revenue_ticket_id', 'r.id'],
    ]);
    expect(result.partials.DISTINCT_ON).toEqual(['e.revenueTicketId']);
    expect(mockGenerateEmbedding).toHaveBeenCalledWith('payment issues');
  });

  it('handles funnel filtering with JOIN', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              funnel: [['name', '=', 'checkout']],
              explanation: 'Filtered by funnel name',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      { userQuery: 'show checkout funnel', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.partials.WHERE).toEqual([['f.name', '=', 'checkout']]);
    expect(result.partials.JOIN).toEqual([
      ['insights_search_funnel as f', 'f.insightId', 'i.insightId'],
    ]);
  });

  it('handles hypotheses filtering with JOIN', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              hypotheses: [['verdict', '=', 'retained']],
              explanation: 'Filtered by hypothesis verdict',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      { userQuery: 'show retained hypotheses', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.partials.WHERE).toEqual([['h.verdict', '=', 'retained']]);
    expect(result.partials.JOIN).toEqual([
      ['insights_search_hypotheses as h', 'h.insightId', 'i.insightId'],
    ]);
  });

  it('handles sorting and limiting', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              sort: [['value', 'desc']],
              limit: [10],
              explanation: 'Top 10 by value',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      { userQuery: 'top 10 by value', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.partials.ORDER_BY).toEqual(['i.value', 'desc']);
    expect(result.partials.LIMIT).toEqual([10]);
  });

  it('handles complex query with multiple components', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({
              basic_filtering: [['incident_time_stamp', '>=', '2025-01-01']],
              funnel: [['name', '=', 'checkout']],
              semantic_search: 'payment errors',
              sort: [['value', 'desc']],
              limit: [5],
              explanation: 'Complex query with multiple filters',
            }),
          },
        };
      },
    });

    const result = await textToSql(
      {
        userQuery: 'top 5 checkout payment errors from January',
        orgId: 'org123',
      } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result.partials.WHERE).toEqual([
      ['i.incident_time_stamp', '>=', '2025-01-01'],
      ['f.name', '=', 'checkout'],
    ]);
    expect(result.partials.JOIN).toEqual([
      ['insights_search_funnel as f', 'f.insightId', 'i.insightId'],
      ['revenue_ticket as r', 'r.insightId', 'i.insightId'],
      ['revenue_ticket_embedding as e', 'e.revenue_ticket_id', 'r.id'],
    ]);
    expect(result.semanticQuery).toEqual({
      text: 'payment errors',
      embedding: [0.1, 0.2, 0.3],
    });
    expect(result.partials.ORDER_BY).toEqual(['i.value', 'desc']);
    expect(result.partials.LIMIT).toEqual([5]);
  });
});
