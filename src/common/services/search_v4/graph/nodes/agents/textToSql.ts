import { Partials, SemanticQuery } from '../../../types.js';
import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './textToSql/systemPrompt.js';
import { LangGraphRunnableConfig } from '@langchain/langgraph';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import config from '@/common/services/search_v4/config.js';
import { withCache } from '@/common/utils/redis.js';
import { getCacheSignature } from '@/common/services/search_v4/graph/caching.js';
import { generateEmbedding } from '@/common/services/openAIService.js';
import { UserQueryModel, WhereTuple, JoinTuple } from './textToSql/types.js';
import { sql } from 'kysely';

const AGENT_NAME = 'textToSql';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const textToSql = async (
  { userQuery, orgId }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ partials: Partials; semanticQuery: SemanticQuery | undefined }> => {
  const messages = [
    { role: 'system', content: systemPrompt(new Date().toISOString().split('T')[0], orgId) },
    { role: 'user', content: userQuery },
  ];

  const response = await withCache(
    getCacheSignature('agent-call', [AGENT_NAME, orgId, userQuery]),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 1 }, // one hour
  );

  // extract LLM response content
  const userQueryModel: UserQueryModel = JSON.parse(response.kwargs.content);

  logger.info('textToSql LLM response', { userQueryModel });

  // Initialize partials object
  const partials: Partials = {
    WHERE: [],
    JOIN: [],
    SELECT: [],
    DISTINCT_ON: [],
    ORDER_BY: undefined,
    LIMIT: undefined,
  };

  let semanticQuery: SemanticQuery | undefined;

  // Process basic filtering
  if (userQueryModel.basic_filtering?.length) {
    const basicWhere = userQueryModel.basic_filtering.map(
      (t: WhereTuple<string>) => ['i.' + t[0], t[1], t[2]] as WhereTuple<string>,
    );
    partials.WHERE = (partials.WHERE || []).concat(basicWhere);
  }

  // Process funnel filtering
  if (userQueryModel.funnel?.length) {
    const funnelWhere = userQueryModel.funnel.map(
      (t: WhereTuple<string>) => ['f.' + t[0], t[1], t[2]] as WhereTuple<string>,
    );
    partials.WHERE = (partials.WHERE || []).concat(funnelWhere);

    // Add funnel JOIN
    const funnelJoin: JoinTuple = ['insights_search_funnel as f', 'f.insightId', 'i.insightId'];
    partials.JOIN = (partials.JOIN || []).concat([funnelJoin]);
  }

  // Process hypotheses filtering
  if (userQueryModel.hypotheses?.length) {
    const hypothesesWhere = userQueryModel.hypotheses.map(
      (t: WhereTuple<string>) => ['h.' + t[0], t[1], t[2]] as WhereTuple<string>,
    );
    partials.WHERE = (partials.WHERE || []).concat(hypothesesWhere);

    // Add hypotheses JOIN
    const hypothesesJoin: JoinTuple = [
      'insights_search_hypotheses as h',
      'h.insightId',
      'i.insightId',
    ];
    partials.JOIN = (partials.JOIN || []).concat([hypothesesJoin]);
  }

  // Process semantic search
  if (userQueryModel.semantic_search) {
    const embeddingRes = await generateEmbedding(userQueryModel.semantic_search);
    semanticQuery = {
      text: userQueryModel.semantic_search,
      embedding: (embeddingRes?.data as Array<{ embedding: number[] }>)[0].embedding,
    };

    // Add semantic search JOINs
    const semanticJoins: JoinTuple[] = [
      ['revenue_ticket as r', 'r.insightId', 'i.insightId'],
      ['revenue_ticket_embedding as e', 'e.revenue_ticket_id', 'r.id'],
    ];
    partials.JOIN = (partials.JOIN || []).concat(semanticJoins);

    // Add semantic search SELECT and DISTINCT_ON
    const semanticSelect = [
      sql`${sql.ref('e.embedding')} <-> ${sql.lit(JSON.stringify(semanticQuery.embedding))}`.as(
        'distance',
      ),
      'e.textValue',
    ];
    partials.SELECT = (partials.SELECT || []).concat(semanticSelect);
    partials.DISTINCT_ON = ['e.revenueTicketId'];
  }

  // Process sorting
  if (userQueryModel.sort?.length) {
    const [column, direction] = userQueryModel.sort[0];
    partials.ORDER_BY = ['i.' + column, direction];
  }

  // Process limit
  if (userQueryModel.limit?.length) {
    partials.LIMIT = [userQueryModel.limit[0]];
  }

  logger.info('textToSql compiled result', { partials, semanticQuery });

  return {
    partials,
    semanticQuery,
  };
};

export default textToSql;
