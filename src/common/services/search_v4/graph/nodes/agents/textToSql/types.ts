import {
  ComparisonOperator,
  OrderByDirectionExpression,
  AliasedRawBuilder,
  TableExpression,
} from 'kysely';
import { DatabaseTables } from '@/types.js';

type WhereTuple<T> = [T, ComparisonOperator, string | number | boolean];
type JoinTuple = [
  TableExpression<DatabaseTables & { i: Record<string, unknown> }, 'i'>,
  string,
  string,
];

interface UserQueryModel {
  sort?: [SortableColumn, OrderByDirectionExpression][];
  limit?: [number];
  basic_filtering?: WhereTuple<keyof BasicFilteringColumns>[];
  funnel?: WhereTuple<keyof FunnelColumns>[];
  hypotheses?: WhereTuple<keyof HypothesesColumns>[];
  semantic_search?: string;
  explanation: string;
}

type GranularityType = 'daily' | 'weekly' | 'monthly';
type AnomalyDetectionMode = 'RP' | 'TCA';
type SensitivityLevel = 'low' | 'medium' | 'high';
type CurrencyType = 'USD' | 'EUR';
type HypothesisVerdict = 'retained' | 'rejected' | 'inconclusive' | 'skipped' | 'untested';
type HypothesisState = 'not-started' | 'failed' | 'skipped' | 'finished';

interface BasicFilteringColumns {
  incident_time_stamp: Date;
  granularity: GranularityType;
  revenue_expected_deviation_pct: number;
  baseline: number;
  revenue_delta: number;
  anomaly_detection_mode: AnomalyDetectionMode;
  sensitivity: SensitivityLevel;
  is_total: boolean;
  is_summary: boolean;
  currency: CurrencyType;
  value: number;
  last_value: number;
}

interface FunnelColumns {
  name: string;
  baseline_value: number;
  actual_value: number;
  impact: number;
}

interface HypothesesColumns {
  hypothesis_id: string;
  verdict: HypothesisVerdict;
  state: HypothesisState;
  inspector: string;
}

type SortableColumn =
  | 'incidentTimeStamp'
  | 'revenueExpectedDeviationPct'
  | 'baseline'
  | 'revenueDelta'
  | 'value'
  | 'lastValue';

interface CompiledSqlQuery {
  WHERE?: WhereTuple<string>[];
  JOIN?: JoinTuple[];
  SELECT?: (AliasedRawBuilder<unknown, 'distance'> | string)[] | undefined;
  DISTINCT_ON?: string[];
  ORDER_BY?: [string, OrderByDirectionExpression];
  LIMIT?: number;
}

export type { UserQueryModel, CompiledSqlQuery, WhereTuple, JoinTuple };
