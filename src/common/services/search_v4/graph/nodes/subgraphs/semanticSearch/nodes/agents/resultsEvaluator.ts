import { ChatGroq } from '@langchain/groq';
import _ from 'lodash';

import systemPrompt from './resultsEvaluator/systemPrompt.js';
import userPrompt from './resultsEvaluator/userPrompt.js';
import { StateAnnotation } from '../../annotations.js';
import { TicketPool } from '@/types.js';
import { PAGE_SIZE } from '@/common/services/search_v2/config.js';
import logger from '@/common/utils/logger.js';
import { getAgentCallSignature, getOffsetSignature } from '../../../../../caching.js';
import * as redis from '@/common/utils/redis.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import config from '@/common/services/search_v4/config.js';
import { Insight, SearchResult, SemanticQuery } from '@/common/services/search_v4/types.js';

const AGENT_NAME = 'resultsEvaluator';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const evaluateChunk = async (
  chunk: TicketPool[],
  semanticQuery: SemanticQuery,
): Promise<SerializedConstructor> => {
  const userPromptStr = userPrompt(
    semanticQuery.text as string,
    chunk.map((r: TicketPool) => ({
      id: r.insightId as number,
      value: r.textValue as string,
    })),
  );

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPromptStr },
  ];

  const promise = redis.withCache(
    getAgentCallSignature(AGENT_NAME, userPromptStr),
    async () => (await model.invoke(messages)).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 24 * 7 },
  );

  return promise;
};

const getNextOffset = (ticketPool: TicketPool[], ids: string[], offset: number): number =>
  _.isEmpty(ids) && _.isEmpty(ticketPool)
    ? offset
    : _.chain(ids)
        .map(id => ticketPool.findIndex(p => p.insightId === id))
        .max()
        .thru(x => offset + (x as number) + 1)
        .value();

// making sure no id is hallucinated
const filterOutHallucinated = (pickedTicketIds: string[], ticketPool: TicketPool[]): string[] =>
  _.uniq(pickedTicketIds).filter(id => ticketPool.some(t => t.insightId === id));

const resultsEvaluator = async ({
  orgId,
  partials,
  semanticQuery,
  candidates,
  lastKnownOffset,
  lastKnownOffsetPage,
  count,
}: typeof StateAnnotation.State): Promise<{ result: SearchResult; lastKnownOffset: number }> => {
  logger.info('Running results evaluator agent', { orgId, partials, lastKnownOffset });
  const chunks = _.chunk(candidates, config.POOL_CHUNK_SIZE);

  const promises = chunks.map(chunk => evaluateChunk(chunk, semanticQuery));
  const responses = await Promise.all(promises);
  const pageIds = responses.flatMap(response => JSON.parse(response?.kwargs?.content)?.ids);
  const finalPageIds = filterOutHallucinated(pageIds, candidates).slice(0, PAGE_SIZE);
  const pickedCandidates = finalPageIds
    .map(id => candidates.find(c => c.insightId === id))
    .filter(x => !!x);
  const pickedInsights = pickedCandidates
    .map(i => i.insightJson as Record<string, unknown>)
    .map(iJson => _.mapKeys(iJson, (_v, k) => _.camelCase(k))) as Insight[];

  const nextOffset = getNextOffset(candidates, finalPageIds, lastKnownOffset);

  logger.info('candidates', { candidates });
  logger.info('lastKnownOffset', { lastKnownOffset });
  logger.info('next offset', { nextOffset, page: lastKnownOffsetPage + 1 });

  await redis.set(
    getOffsetSignature(orgId, lastKnownOffsetPage + 1, partials),
    nextOffset,
    60 * 60 * 8,
  );

  //logger.info('Results evaluator agent final page insights', { pickedInsights });

  return {
    result: {
      insights: pickedInsights,
      filteredCount: count,
    },
    lastKnownOffset: nextOffset,
  };
};

export default resultsEvaluator;
