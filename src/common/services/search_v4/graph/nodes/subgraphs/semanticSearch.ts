import { StateAnnotation as ParentStateAnnotation } from '../../annotations.js';
import subgraph from './semanticSearch/graph.js';
import { SearchResult } from '../../../types.js';

const findPoolOffset = async ({
  page,
  orgId,
  partials,
  baseQuery,
  semanticQuery,
}: typeof ParentStateAnnotation.State): Promise<{ result: SearchResult }> => {
  const response = await subgraph.invoke({
    page,
    orgId,
    partials,
    baseQuery,
    semanticQuery,
  });

  return {
    result: response.result,
  };
};

export default findPoolOffset;
