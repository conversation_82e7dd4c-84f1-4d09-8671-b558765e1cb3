import { Partials } from '../types.js';

export const getCacheSignature = (namespace: string, args: unknown[]): string =>
  `${namespace}:${args.map(x => JSON.stringify(x)).join('|')}`;

export const getOffsetSignature = (orgId: string, page: number, partials: Partials): string =>
  getCacheSignature('pool-offset', [orgId, page, partials]);

export const getAgentCallSignature = (agentName: string, userPromptArgs: unknown) =>
  getCacheSignature('agent-call', [agentName, userPromptArgs]);

export const getTotalCountSignature = (orgId: string, partials: Partials): string =>
  getCacheSignature('total-count', [orgId, partials]);
