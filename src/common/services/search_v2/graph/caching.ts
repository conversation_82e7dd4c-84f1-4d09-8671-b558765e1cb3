import { SearchElements } from '@/types.js';

const elementsSignature = (e: SearchElements): string =>
  [e.where<PERSON><PERSON><PERSON>, e.semanticQuery, e.includesProductCategory].join('|');

export const getOffsetSignature = (orgId: string, elements: SearchElements, page: number): string =>
  ['search-pool-offset', orgId, elementsSignature(elements), page].join(':');

export const getUserQuerySignature = (orgId: string, elements: SearchElements): string =>
  `${orgId}:${elementsSignature(elements)}`;

export const getTotalCountSignature = (orgId: string, elements: SearchElements): string =>
  `search-total-count:${getUserQuerySignature(orgId, elements)}`;

export const getAgentCallSignature = (agentName: string, userPromptArgs: unknown) => {
  return `agent-call:${agentName}:${JSON.stringify(userPromptArgs)}`;
};
