import { SearchResult } from '../types.js';
import { SearchElements } from '@/types.js';
import { Annotation } from '@langchain/langgraph';

const InputAnnotation = Annotation.Root({
  userQuery: Annotation<string>,
  page: Annotation<number>,
  orgId: Annotation<string>,
  // There's a known bug where the types check won't pass. the stateSchema property in
  // graph.ts will be highlighted as wrong by typescript. I've found that the only way
  // to get it to work is the input to have a property that is not present in the state.
  // See also: https://github.com/langchain-ai/langgraphjs/issues/737
  x: Annotation<boolean>,
});

const OutputAnnotation = Annotation.Root({
  result: Annotation<SearchResult>,
});

const StateAnnotation = Annotation.Root({
  userQuery: Annotation<string>,
  page: Annotation<number>,
  orgId: Annotation<string>,
  userQueryBreakdown: Annotation<SearchElements>,
  resultIds: Annotation<string[]>,
  resultCount: Annotation<number>,
  result: Annotation<SearchResult>,
});

export { StateAnnotation, InputAnnotation, OutputAnnotation };
