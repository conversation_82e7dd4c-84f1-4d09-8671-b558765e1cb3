import { StateAnnotation as ParentStateAnnotation } from '../../annotations.js';
import subgraph from './semanticSearch/graph.js';
import logger from '@/common/utils/logger.js';

const semanticSearch = async ({
  page,
  orgId,
  userQueryBreakdown,
}: typeof ParentStateAnnotation.State): Promise<{ resultIds: string[]; resultCount: number }> => {
  const response = await subgraph.invoke({
    userQueryDetails: {
      page,
      orgId,
    },
    userQueryBreakdown,
  });

  logger.info('semanticSearch result', response);

  return {
    resultIds: response.insightIds,
    resultCount: response.insightsCount,
  };
};

export default semanticSearch;
