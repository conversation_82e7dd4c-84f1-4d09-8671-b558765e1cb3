import { findLastKnownOffset } from './findLastKnownOffset.js';
import { getOffsetSignature } from '../../../../../caching.js';
import * as redis from '@/common/utils/redis.js';
import '../../../../../../../../../../test/tools/useRedis.js';
import { SearchElements } from '@/types.js';

describe('findLastKnownOffset', () => {
  const mockOrgId = 'test-org-123';
  const mockQueryBreakdown: SearchElements = { semanticQuery: 'test query' };

  beforeEach(async () => {
    await redis.client.flushAll();
  });

  afterAll(async () => {
    await redis.client.flushAll();
  });

  it('should return 0 offset when page is 1', async () => {
    const result = await findLastKnownOffset({
      userQueryDetails: {
        page: 1,
        orgId: mockOrgId,
      },
      userQueryBreakdown: mockQueryBreakdown,
    });

    expect(result).toEqual({
      lastKnownOffset: 0,
      lastKnownOffsetPage: 1,
    });
  });

  it('should return the cached offset when it exists', async () => {
    const page = 6;
    const expectedOffset = 50;
    const cacheKey = getOffsetSignature(mockOrgId, mockQueryBreakdown, page);
    await redis.set(cacheKey, expectedOffset);

    const result = await findLastKnownOffset({
      userQueryDetails: {
        page,
        orgId: mockOrgId,
      },
      userQueryBreakdown: mockQueryBreakdown,
    });

    expect(result).toEqual({
      lastKnownOffset: expectedOffset,
      lastKnownOffsetPage: page,
    });
  });

  it('should return page 1 when cached offset doesnt exist', async () => {
    const page = 6;

    const result = await findLastKnownOffset({
      userQueryDetails: {
        page,
        orgId: mockOrgId,
      },
      userQueryBreakdown: mockQueryBreakdown,
    });

    expect(result).toEqual({
      lastKnownOffset: 0,
      lastKnownOffsetPage: 1,
    });
  });

  it('should find the last known offset in previous pages', async () => {
    const currentPage = 5;
    const lastKnownPage = 3;
    const expectedOffset = 100;

    const cacheKey = getOffsetSignature(mockOrgId, mockQueryBreakdown, lastKnownPage);
    await redis.set(cacheKey, expectedOffset);

    const result = await findLastKnownOffset({
      userQueryDetails: {
        page: currentPage,
        orgId: mockOrgId,
      },
      userQueryBreakdown: mockQueryBreakdown,
    });

    expect(result).toEqual({
      lastKnownOffset: expectedOffset,
      lastKnownOffsetPage: lastKnownPage,
    });
  });

  it('should ignore invalid cache values', async () => {
    const page = 3;
    const cacheKey = getOffsetSignature(mockOrgId, mockQueryBreakdown, page);
    await redis.set(cacheKey, 'invalid-number');

    const result = await findLastKnownOffset({
      userQueryDetails: {
        page,
        orgId: mockOrgId,
      },
      userQueryBreakdown: mockQueryBreakdown,
    });

    expect(result).toEqual({
      lastKnownOffset: 0,
      lastKnownOffsetPage: 1,
    });
  });
});
