import { generateEmbedding } from '@/common/services/openAIService.js';
import { SearchElements } from '@/types.js';
import logger from '@/common/utils/logger.js';
import { POOL_SIZE } from '@/common/services/search_v2/config.js';

const getEmbedding = async (query: string): Promise<number[]> => {
  const { data } = await generateEmbedding(query);
  return data[0].embedding;
};

const whereStatementBuilder = (
  orgId: string,
  whereClause?: string,
  includesProductCategory?: boolean,
): string => {
  let whereStatement = `WHERE rt.org_id = '${orgId}' AND rt.visible = true `;
  if (whereClause) whereStatement += `AND ${whereClause} `;
  if (!includesProductCategory) whereStatement += "AND rte.revenue_ticket_field <> 'category'";

  return whereStatement;
};

const semanticQueryBuilder = async (
  orgId: string,
  elements: SearchElements,
  poolOffset: number,
): Promise<string> => {
  const { whereClause, semanticQuery, includesProductCategory } = elements;

  const userQueryEmbedding = await getEmbedding(semanticQuery);
  const whereStatement = whereStatementBuilder(orgId, whereClause, includesProductCategory);

  const sqlQuery =
    `
WITH min_distances AS (
  SELECT DISTINCT ON (rte.revenue_ticket_id)
    rte.revenue_ticket_id,
    rte.text_value,
    rte.embedding <-> '[${userQueryEmbedding}]' AS distance,
    rt.raw_grape_json,
    rt.incident_time_stamp,
    rt.id,
    rt.insight_id
  FROM
    revenue_ticket_embedding rte
    JOIN revenue_ticket rt ON rt.id = rte.revenue_ticket_id
  ` +
    whereStatement +
    `
  ORDER by
    rte.revenue_ticket_id,
    distance ASC
)
SELECT *
FROM min_distances
WHERE distance < 1.2
ORDER BY distance ASC, incident_time_stamp DESC, id ASC
LIMIT ${POOL_SIZE}
OFFSET ${poolOffset}`;

  logger.info('Built sql query', { sqlQuery });
  return sqlQuery;
};

export { semanticQueryBuilder };
