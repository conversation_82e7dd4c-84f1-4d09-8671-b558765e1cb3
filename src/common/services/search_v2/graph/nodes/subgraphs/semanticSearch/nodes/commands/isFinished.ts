import { Command } from '@langchain/langgraph';
import { StateAnnotation } from '../../annotations.js';
import logger from '@/common/utils/logger.js';

const isFinished = ({
  userQueryDetails: { page },
  lastKnownOffsetPage,
}: typeof StateAnnotation.State) => {
  logger.info('isFinished', { page, lastKnownOffsetPage });

  if (page === lastKnownOffsetPage) {
    logger.info('Found the correct page');
    return new Command({
      goto: 'get-total-count',
    });
  }

  logger.info('Reiterating subgraph with page', { page: lastKnownOffsetPage + 1 });
  return new Command({
    update: {
      lastKnownOffsetPage: ++lastKnownOffsetPage,
    },
    goto: 'get-one-page',
  });
};

export default isFinished;
