import { StateGraph } from '@langchain/langgraph';

import { StateAnnotation, InputAnnotation, OutputAnnotation } from './annotations.js';
import resultsEvaluator from './nodes/agents/resultsEvaluator.js';
import isFinished from './nodes/commands/isFinished.js';
import findLastKnownOffset from './nodes/tools/findLastKnownOffset.js';
import getOnePage from './nodes/tools/getOnePage.js';
import getTotalCount from './nodes/tools/getTotalCount.js';

const workflow = new StateGraph({
  input: InputAnnotation,
  output: OutputAnnotation,
  stateSchema: StateAnnotation,
})
  .addNode('find-last-known-offset', findLastKnownOffset)
  .addNode('get-one-page', getOnePage)
  .addNode('results-evaluator', resultsEvaluator)
  .addNode('is-finished', isFinished, { ends: ['get-one-page', 'get-total-count'] })
  .addNode('get-total-count', getTotalCount)
  .addEdge('__start__', 'find-last-known-offset')
  .addEdge('find-last-known-offset', 'get-one-page')
  .addEdge('get-one-page', 'results-evaluator')
  .addEdge('results-evaluator', 'is-finished');

const graph = workflow.compile();

export default graph;
