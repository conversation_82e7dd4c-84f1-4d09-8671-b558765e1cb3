import { TicketPool } from '@/types.js';
import { pgPool as postgres } from '@/common/utils/pgConn.js';
import logger from '@/common/utils/logger.js';
import { semanticQueryBuilder } from './getOnePage/sql.js';
import { StateAnnotation } from '../../annotations.js';

async function getOnePage({
  userQueryDetails: { orgId },
  userQueryBreakdown,
  lastKnownOffset,
}: typeof StateAnnotation.State): Promise<{ candidates: TicketPool[] }> {
  const candidatesQuery = await semanticQueryBuilder(orgId, userQueryBreakdown, lastKnownOffset);
  const { rows: candidates } = await postgres.query<TicketPool>(candidatesQuery);
  logger.info('Sql query candidates result', {
    candidates: candidates.map((r: TicketPool) => r.insight_id),
  });
  return { candidates };
}

export default getOnePage;
