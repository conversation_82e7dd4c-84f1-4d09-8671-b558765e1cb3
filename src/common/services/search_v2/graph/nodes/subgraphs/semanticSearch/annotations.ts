import { SearchElements, TicketPool } from '@/types.js';
import { Annotation } from '@langchain/langgraph';

interface UserQueryDetails {
  page: number;
  orgId: string;
}

const InputAnnotation = Annotation.Root({
  userQueryDetails: Annotation<UserQueryDetails>,
  userQueryBreakdown: Annotation<SearchElements>,
  x: Annotation<boolean>,
});

const OutputAnnotation = Annotation.Root({
  insightIds: Annotation<string[]>,
  insightsCount: Annotation<number>,
});

const StateAnnotation = Annotation.Root({
  userQueryDetails: Annotation<UserQueryDetails>,
  userQueryBreakdown: Annotation<SearchElements>,
  lastKnownOffset: Annotation<number>,
  lastKnownOffsetPage: Annotation<number>,
  candidates: Annotation<TicketPool[]>,
  insightIds: Annotation<string[]>,
  insightsCount: Annotation<number>,
});

export { StateAnnotation, InputAnnotation, OutputAnnotation };
