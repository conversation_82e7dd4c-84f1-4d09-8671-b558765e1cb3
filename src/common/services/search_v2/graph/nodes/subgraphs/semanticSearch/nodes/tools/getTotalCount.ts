import * as redis from '@/common/utils/redis.js';
import { PAGE_SIZE } from '../../../../../../config.js';
import { getTotalCountSignature } from '../../../../../caching.js';
import _ from 'lodash';
import { StateAnnotation } from '../../annotations.js';
import logger from '@/common/utils/logger.js';

const calculateTotalCount = (page: number, resultIds: string[]): number =>
  (page - 1) * PAGE_SIZE + resultIds.length;

async function getTotalCountNumber(
  totalCountSignature: string,
  page: number,
  resultIds: string[],
): Promise<number> {
  const cachedTotalCount = await redis.get<number>(totalCountSignature);
  if (!_.isNull(cachedTotalCount)) return cachedTotalCount;
  if (resultIds.length === PAGE_SIZE) return -1; // is not cached, cannot calculate (is not last page)

  const totalCount = calculateTotalCount(page, resultIds); // is not cached, can calculate (is last page)
  await redis.set(totalCountSignature, totalCount, 8 * 60 * 60);
  return totalCount;
}

async function getTotalCount({
  userQueryDetails: { orgId, page },
  userQueryBreakdown,
  insightIds,
}: typeof StateAnnotation.State): Promise<{ insightsCount: number }> {
  const insightsCount = await getTotalCountNumber(
    getTotalCountSignature(orgId, userQueryBreakdown),
    page,
    insightIds,
  );

  logger.info('Total count', { insightsCount });

  return { insightsCount };
}

export default getTotalCount;
