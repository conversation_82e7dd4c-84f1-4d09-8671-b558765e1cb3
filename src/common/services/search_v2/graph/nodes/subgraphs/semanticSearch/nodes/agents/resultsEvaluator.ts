import { ChatOpenAI } from '@langchain/openai';
import _ from 'lodash';

import systemPrompt from './resultsEvaluator/systemPrompt.js';
import userPrompt from './resultsEvaluator/userPrompt.js';
import tools from './resultsEvaluator/tools.js';
import { StateAnnotation } from '../../annotations.js';
import { TicketPool } from '@/types.js';
import { PAGE_SIZE } from '@/common/services/search_v2/config.js';
import logger from '@/common/utils/logger.js';
import { withCache } from '@/common/utils/redis.js';
import { getAgentCallSignature, getOffsetSignature } from '../../../../../caching.js';
import * as redis from '@/common/utils/redis.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import { LangGraphRunnableConfig } from '@langchain/langgraph';

const AGENT_NAME = 'resultsEvaluator';

const model = new ChatOpenAI({
  model: 'gpt-4o',
  temperature: 0,
}).bindTools(tools);

const getNextOffset = (ticketPool: TicketPool[], ids: string[], offset: number): number =>
  _.isEmpty(ids) && _.isEmpty(ticketPool)
    ? offset
    : _.chain(ids)
        .map(id => ticketPool.findIndex(p => p.insight_id === id))
        .max()
        .thru(x => offset + (x as number) + 1)
        .value();

// making sure no id is hallucinated
const filterOutHallucinated = (pickedTicketIds: string[], ticketPool: TicketPool[]): string[] =>
  _.uniq(pickedTicketIds).filter(id => ticketPool.some(t => t.insight_id === id));

const resultsEvaluator = async (
  {
    userQueryDetails: { orgId },
    userQueryBreakdown,
    candidates,
    lastKnownOffset,
    lastKnownOffsetPage,
  }: typeof StateAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ insightIds: string[]; lastKnownOffset: number }> => {
  const userPromptStr = userPrompt(
    userQueryBreakdown.semanticQuery,
    candidates.map((r: TicketPool) => ({ id: r.insight_id, value: r.text_value })),
  );

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPromptStr },
  ];

  const response = await withCache(
    getAgentCallSignature(AGENT_NAME, userPromptStr),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 24 * 7 },
  );

  logger.info('Results evaluator agent response: ', { response });

  const pageIds = response?.kwargs?.tool_calls?.[0].args?.ids;
  if (!pageIds) throw new Error('No filtered IDs were found');

  const finalPageIds = filterOutHallucinated(pageIds, candidates).slice(0, PAGE_SIZE);
  const nextOffset = getNextOffset(candidates, finalPageIds, lastKnownOffset);
  await redis.set(
    getOffsetSignature(orgId, userQueryBreakdown, lastKnownOffsetPage + 1),
    nextOffset,
    60 * 60 * 8,
  );

  logger.info('Results evaluator agent final page ids', { finalPageIds });

  return {
    insightIds: finalPageIds,
    lastKnownOffset: nextOffset,
  };
};

export default resultsEvaluator;
