import { StateAnnotation } from '../../annotations.js';
import { getInsights } from '@/http/services/insights.js';
import { Insight, SearchResult } from '../../../types.js';
import logger from '@/common/utils/logger.js';

async function retriever({
  orgId,
  resultIds,
  resultCount,
}: typeof StateAnnotation.State): Promise<{ result: SearchResult }> {
  logger.info('Retrieving insights by IDs', { resultIds });

  const insights = (await getInsights(
    { skip: undefined, limit: undefined },
    {
      insightIds: resultIds,
      uuids: undefined,
      orgIds: [orgId],
      startDate: undefined,
      endDate: undefined,
      isSummary: undefined,
      isTotal: undefined,
      breakdownKey: undefined,
      breakdownValue: undefined,
      granularity: undefined,
    },
  )) as Insight[];

  const sortedInsights = resultIds
    .map(id => insights.find(insight => insight.displayId === id))
    .filter(x => !!x); //remove nulls coming from tickets that exist in the search index but not in the insights table

  return {
    result: {
      insights: sortedInsights,
      filteredCount: resultCount,
    },
  };
}

export default retriever;
