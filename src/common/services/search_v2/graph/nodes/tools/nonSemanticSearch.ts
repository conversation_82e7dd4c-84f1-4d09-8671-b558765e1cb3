import { SearchElements, TicketPool } from '@/types.js';

import { StateAnnotation } from '../../annotations.js';
import { pgPool as postgres } from '@/common/utils/pgConn.js';
import logger from '@/common/utils/logger.js';
import { PAGE_SIZE } from '../../../config.js';

const nonSemanticQueryBuilder = async (
  orgId: string,
  whereClause: string | undefined,
  page = 1,
): Promise<string> => {
  const offset = (page - 1) * PAGE_SIZE;

  const sqlQuery = `SELECT insight_id FROM revenue_ticket rt WHERE rt.org_id = '${orgId}' AND rt.visible = true ${
    whereClause ? 'AND ' + whereClause : ''
  } ORDER BY incident_time_stamp DESC, id ASC LIMIT ${PAGE_SIZE} OFFSET ${offset}`;

  return sqlQuery;
};

const nonSemanticCountQueryBuilder = (
  orgId: string,
  { whereClause }: { whereClause?: string },
): string =>
  `SELECT COUNT(*) AS counter FROM revenue_ticket rt WHERE rt.org_id = '${orgId}' ${
    whereClause ? 'AND ' + whereClause : ''
  }`;

async function getNonSemanticResults(
  orgId: string,
  { whereClause }: SearchElements,
  page: number,
): Promise<string[]> {
  const query = await nonSemanticQueryBuilder(orgId, whereClause, page);
  logger.info('Running non-semantic sql query', { query });

  const { rows: insights } = await postgres.query<TicketPool>(query);
  const insightIds = insights.map((i: TicketPool) => i.insight_id);
  logger.info('Sql query results', { insightIds });

  return insightIds;
}

async function getNonSemanticCount(orgId: string, elements: SearchElements): Promise<number> {
  const query = nonSemanticCountQueryBuilder(orgId, elements);
  const { rows } = await postgres.query<{ counter: string }>(query);
  return parseInt(rows[0].counter);
}

async function nonSemanticSearch({
  orgId,
  userQueryBreakdown,
  page,
}: typeof StateAnnotation.State): Promise<{ resultIds: string[]; resultCount: number }> {
  logger.info('Running non-semantic query', { userQueryBreakdown, page, orgId });
  const [insightIds, filteredCount] = await Promise.all([
    getNonSemanticResults(orgId, userQueryBreakdown, page),
    getNonSemanticCount(orgId, userQueryBreakdown),
  ]);
  return { resultIds: insightIds, resultCount: filteredCount };
}

export default nonSemanticSearch;
