import { ChatOpenAI } from '@langchain/openai';

import systemPrompt from './queryBreakdown/systemPrompt.js';
import userPrompt from './queryBreakdown/userPrompt.js';
import tools from './queryBreakdown/tools.js';
import { InputAnnotation } from '../../annotations.js';
import { SearchElements } from '@/types.js';
import logger from '@/common/utils/logger.js';
import { withCache } from '@/common/utils/redis.js';
import { getAgentCallSignature } from '../../caching.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import { LangGraphRunnableConfig } from '@langchain/langgraph';

const AGENT_NAME = 'queryBreakdown';

const model = new ChatOpenAI({
  model: 'gpt-4o',
  temperature: 0,
}).bindTools(tools);

const callModel = async (
  { userQuery }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ userQueryBreakdown: SearchElements }> => {
  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt(userQuery) },
  ];

  const response = await withCache(
    getAgentCallSignature(AGENT_NAME, userQuery),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 24 * 7 },
  );

  logger.info('Query breakdown agent response: ', { response });

  const userQueryBreakdown = response.kwargs.tool_calls?.[0]?.args as SearchElements;
  if (!userQueryBreakdown) throw new Error('No user query breakdown found');

  return { userQueryBreakdown };
};

export default callModel;
