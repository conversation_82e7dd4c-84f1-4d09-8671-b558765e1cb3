const tools = [
  {
    type: 'function',
    function: {
      name: 'build_sql_query',
      description:
        'Given all the information necessary, construct an SQL query against a Postgres DB.',
      parameters: {
        type: 'object',
        properties: {
          whereClause: {
            type: 'string',
            description: 'The WHERE clause of the SQL query.',
          },
          semanticQuery: {
            type: 'string',
            description:
              'A string that will be converted to an embedding and will be queried against other embeddings in the DB, to find semantically similar strings.',
          },
          includesProductCategory: {
            type: 'boolean',
            description:
              'Whether the user input query includes the notion of product category. Will be used to decide whether to query the product category data.',
          },
        },
        required: ['includesProductCategory'],
      },
    },
  },
];

export default tools;
