import { StateGraph } from '@langchain/langgraph';

import { StateAnnotation, InputAnnotation, OutputAnnotation } from './annotations.js';
import queryBreakdown from './nodes/agents/queryBreakdown.js';
import nonSemanticSearch from './nodes/tools/nonSemanticSearch.js';
import semanticSearch from './nodes/subgraphs/semanticSearch.js';
import isSemantic from './nodes/conditionals/isSemantic.js';
import insightsRetriever from './nodes/tools/insightsRetriever.js';

const workflow = new StateGraph({
  input: InputAnnotation,
  output: OutputAnnotation,
  stateSchema: StateAnnotation,
})
  .addNode('query-breakdown', queryBreakdown)
  .addNode('non-semantic-search', nonSemanticSearch)
  .addNode('semantic-search', semanticSearch)
  .addNode('insights-retriever', insightsRetriever)
  .addEdge('__start__', 'query-breakdown')
  .addConditionalEdges('query-breakdown', isSemantic, {
    true: 'semantic-search',
    false: 'non-semantic-search',
  })
  .addEdge('semantic-search', 'insights-retriever')
  .addEdge('non-semantic-search', 'insights-retriever');

const graph = workflow.compile();

export default graph;
