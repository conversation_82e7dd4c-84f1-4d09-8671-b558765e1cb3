import { SearchResult } from '../types.js';
import graph from '../graph/graph.js';
import { CallbackHandler } from 'langfuse-langchain';

interface Metadata {
  userEmail: string;
  env: string;
  sessionId: string;
}

interface Query {
  userQuery: string;
  orgId: string;
  page?: number;
}

const invoke = async (query: Query, metadata: Metadata): Promise<SearchResult> => {
  const langfuseHandler = new CallbackHandler({
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    baseUrl: process.env.LANGFUSE_HOST,
    userId: metadata.userEmail,
    sessionId: metadata.sessionId,
    metadata: {
      env: metadata.env,
    },
    flushAt: 1,
  });
  const { result } = await graph.invoke(query, {
    callbacks: [langfuseHandler],
    runName: 'insights-app-search',
  });
  await langfuseHandler.shutdownAsync();
  return result;
};

export { invoke };
