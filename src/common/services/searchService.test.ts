import '../../../test/tools/usePostgres';
import { pgPool } from '../utils/pgConn.js';
import testTicket from '../../../test/resources/airflow/testTicket.json' with { type: 'json' };
import { toDatabaseSchema } from './searchService/utils.js';
import { RevenueTicket, Insight } from '../../types.js';
import _ from 'lodash';

// Define the function type to match the actual implementation
type IndexInsightsForSearchType = (insights: Insight[]) => Promise<unknown>;

// Mock OpenAI service
const openAIServiceMock = {
  generateEmbedding: jest.fn(() => ({
    data: [{ embedding: [1, 1, 1] }],
    usage: { total_tokens: 5 },
  })),
};

// Use unstable_mockModule to register the mock before the module under test is imported
// @ts-expect-error - see https://jestjs.io/docs/ecmascript-modules#module-mocking-in-esm
jest.unstable_mockModule('@/common/services/openAIService.js', () => openAIServiceMock);

// Declare with proper type
let indexInsightsForSearch: IndexInsightsForSearchType;

beforeAll(async () => {
  // Dynamically import the module AFTER the mock is set up
  const module = await import('./searchService.js');
  indexInsightsForSearch = module.indexInsightsForSearch;
});

// tests
describe('indexInsightsForSearch', () => {
  const createdTestTicket: Insight = { ...testTicket, displayId: 1 };
  it('should create a new ticket', async () => {
    // create ticket

    const notifications: Insight[] = [createdTestTicket];

    await indexInsightsForSearch(notifications);

    // retrieve created ticket and remove id for comparison
    const { id: _id, ...created } = (
      await pgPool.query<RevenueTicket>('SELECT * FROM revenue_ticket WHERE insight_id = $1;', [1])
    ).rows[0];

    // stringify date and json for comparison
    const createdForComparison = {
      ...created,
      incident_time_stamp: (created.incident_time_stamp as Date).toISOString(),
      raw_grape_json: JSON.stringify(created.raw_grape_json),
    };

    expect(createdForComparison).toEqual(toDatabaseSchema(createdTestTicket));
  });

  it('should fail to create an already existing ticket', async () => {
    // create ticket
    const notifications: Insight[] = [createdTestTicket, createdTestTicket];

    await expect(indexInsightsForSearch(notifications)).rejects.toThrow(
      'duplicate key value violates unique constraint "revenue_ticket_insight_id_key"',
    );
  });

  it('should fail to create an ticket with missing fields', async () => {
    const testTicketWithoutOrg: Insight = {
      ...createdTestTicket,
      displayId: 321,
      id: crypto.randomUUID(),
    };
    _.unset(testTicketWithoutOrg, ['orgId']);

    // create ticket
    const notifications: Insight[] = [testTicketWithoutOrg];

    await expect(indexInsightsForSearch(notifications)).rejects.toThrow(
      'null value in column "org_id" of relation "revenue_ticket" violates not-null constraint',
    );
  });
});
