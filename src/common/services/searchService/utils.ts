import { formatDateRespectingTimezone } from '../../utils/dateUtils.js';
import _ from 'lodash';
import * as config from './config.js';
import type { Insight, RevenueTicket } from '../../../types.js';

export const toDatabaseSchema = (insight: Insight): RevenueTicket => {
  const impacts = _.keyBy(insight.funnelMetrics as Record<string, unknown>[], 'id');

  return {
    insight_id: insight.displayId,
    org_id: insight.orgId,
    visible: insight.visible,
    granularity: insight.granularity,
    is_summary: insight.isSummary,
    currency: (insight.keyMetric as { symbol: string })?.symbol,
    revenue_expected_deviation_pct: insight.revenueExpectedDeviationPct,
    revenue_delta: insight.revenueDelta,
    product_views_revenue_impact: impacts.itemsviewed?.keyMetricImpact,
    carts_revenue_impact: impacts.itemsaddedtocart?.keyMetricImpact,
    checkouts_revenue_impact: impacts.itemscheckedout?.keyMetricImpact,
    orders_revenue_impact: impacts.itemspurchased?.keyMetricImpact,
    aov_revenue_impact: impacts['itemrevenue/itemavgvalue']?.keyMetricImpact,
    revenue_actual: impacts.itemrevenue?.actualValue,
    incident_time_stamp: insight.incidentTimeStamp
      ? new Date(formatDateRespectingTimezone(insight.incidentTimeStamp as string)).toISOString()
      : undefined,
    sensitivity: insight.sensitivity,
    issue_status: 'unresolved', // legacy
    raw_grape_json: '{}', // legacy
  };
};

export const extractEmbeddingTexts = (insight: Insight, configParam: typeof config): string[] =>
  configParam.embeddingFields
    .map(field => insight[field])
    .map(value => (Array.isArray(value) ? value.join(' ') : (value as string)));
