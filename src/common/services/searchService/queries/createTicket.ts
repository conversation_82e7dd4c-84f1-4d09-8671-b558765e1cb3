import 'dotenv/config';
import * as config from '../config.js';
import * as openAIService from '@/common/services/openAIService.js';
import { toDatabaseSchema, extractEmbeddingTexts } from '../utils.js';
import logger from '../../../../common/utils/logger.js';
import { kysely } from '../../../utils/pgConn.js';
import { DatabaseTables, EmbeddingRecord, Insight } from '../../../../types.js';
import { ExpressionBuilder, QueryCreator } from 'kysely';

type ValueBuilder = ExpressionBuilder<DatabaseTables, keyof DatabaseTables>;

const getEmbeddings = (texts: string[]): Promise<EmbeddingRecord[]> =>
  Promise.all(texts.map(t => openAIService.generateEmbedding(t)));

const mapEmbeddings = (
  eb: ValueBuilder,
  embeddings: EmbeddingRecord[],
  texts: string[],
  configParam: typeof config,
): EmbeddingRecord[] =>
  embeddings.map((e, i) => ({
    revenue_ticket_id: eb.selectFrom('inserted_ticket').select('id'),
    revenue_ticket_field:
      configParam.embeddingFields[i] === 'title' ? 'category' : configParam.embeddingFields[i],
    text_value: texts[i],
    embedding: `[${(e as { data: [{ embedding: unknown }] }).data[0].embedding}]`,
    platform: configParam.platform,
    model: configParam.embeddingsModel,
    cost: (e as { usage: { total_tokens: number } }).usage.total_tokens,
    cost_unit: 'token',
    model_params: JSON.stringify(configParam.embeddingsModelParams),
  }));

function getCreateTicketSql(
  insight: Insight,
  texts: string[],
  embeddings: EmbeddingRecord[],
  configParam: typeof config,
) {
  const insightFields = toDatabaseSchema(insight);

  const compiledQuery = kysely
    .with('inserted_ticket', (eb: QueryCreator<DatabaseTables>) =>
      eb.insertInto('revenue_ticket').values(insightFields).returning('id'),
    )
    .insertInto('revenue_ticket_embedding')
    .values((eb: ValueBuilder) => mapEmbeddings(eb, embeddings, texts, configParam))
    .compile();

  return { text: compiledQuery.sql, values: compiledQuery.parameters };
}

const createTicket = async (insight: Insight) => {
  logger.info(`Creating search index query for insight ${insight.id}`);
  const texts = extractEmbeddingTexts(insight, config);
  const embeddings = await getEmbeddings(texts);
  const newTicketSql = getCreateTicketSql(insight, texts, embeddings, config);

  return newTicketSql;
};

export default createTicket;
