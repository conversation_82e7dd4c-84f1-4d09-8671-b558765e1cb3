import _ from 'lodash';

// Set up the ESM mock before importing createTicket
const openAIServiceMock = {
  generateEmbedding: jest.fn(() => ({
    data: [{ embedding: [0.1, 0.2, 0.3] }],
    usage: { total_tokens: 5 },
  })),
};

// @ts-expect-error - see https://jestjs.io/docs/ecmascript-modules#module-mocking-in-esm
jest.unstable_mockModule('@/common/services/openAIService', () => openAIServiceMock);

// Now dynamically import createTicket AFTER setting up the mock
const { default: createTicket } = await import('./createTicket.js');

import testTicket from '../../../../../test/resources/airflow/testTicket.json' with { type: 'json' };
import expectedResult from '../../../../../test/resources/importTicketSql.json' with { type: 'json' };
import expectedResultMissingCheckouts from '../../../../../test/resources/importTicketSqlMissingCheckouts.json' with { type: 'json' };

describe('createTicketQuery', () => {
  it('should return a string', async () => {
    const result = await createTicket(testTicket);
    expect(result).toEqual(expectedResult);
  });

  it('should work for a missing funnel step', async () => {
    const testTicketWithMissingField = {
      ...testTicket,
      funnelMetrics: _.reject(testTicket.funnelMetrics, { id: 'itemscheckedout' }),
    };

    const result = await createTicket(testTicketWithMissingField);
    expect(result).toEqual(expectedResultMissingCheckouts);
  });
});
