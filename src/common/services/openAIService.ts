import OpenAI from 'openai';
import * as redis from '../utils/redis.js';
import _ from 'lodash';
import logger from '../utils/logger.js';

type Agent = {
  name: string;
  systemPrompt: string;
  userPrompt: (...args: unknown[]) => string;
  tools: OpenAI.ChatCompletionTool[];
};

const getEmbeddingSignature = (text: string): string => `embedding:${text}`;

const generateEmbedding = async (
  input: string,
  model = 'text-embedding-3-large',
  dimensions = 1024,
) => {
  const cachedEmbedding = await redis.get<string>(getEmbeddingSignature(input));
  if (!_.isNull(cachedEmbedding)) {
    logger.info('Embedding cache hit', { input });
    return JSON.parse(cachedEmbedding);
  }

  logger.info('Generating embedding', { input });
  const openAI = new OpenAI({
    timeout: 5 * 1000,
    maxRetries: 3,
  });
  const payload = { input, model, dimensions };
  const res = await openAI.embeddings.create(payload);
  redis.set(getEmbeddingSignature(input), JSON.stringify(res), 60 * 60 * 48);
  return res;
};

const callFunction = async ({
  agent,
  userPromptArgs,
  model = 'gpt-4o',
  functionName,
}: {
  agent: Agent;
  userPromptArgs: unknown[];
  model?: string;
  functionName?: string;
}) => {
  const { systemPrompt, userPrompt, tools } = agent;
  const messages: OpenAI.ChatCompletionMessageParam[] = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt(...userPromptArgs) },
  ];
  const toolChoice: OpenAI.ChatCompletionToolChoiceOption = {
    type: 'function',
    function: { name: functionName ?? tools[0].function.name },
  };

  const openAI = new OpenAI({
    timeout: 10 * 1000,
    maxRetries: 3,
  });

  return openAI.chat.completions.create({
    model,
    messages,
    tools,
    tool_choice: toolChoice,
    seed: 0,
  });
};

const callAgent = async (agent: Agent, userPromptArgs: unknown[]) => {
  const response = await callFunction({ agent, userPromptArgs });
  const result = JSON.parse(
    response.choices[0]?.message?.tool_calls?.[0]?.function?.arguments ?? '{}',
  );
  logger.info(`Agent ${agent.name} result`, { result });
  return result;
};

const agentCallSignature = (agent: Agent, userPromptArgs: unknown[]): string => {
  return `agent-call:${agent.name}:${JSON.stringify(userPromptArgs)}`;
};

const callAgentWithCache = async (
  agent: Agent,
  userPromptArgs: unknown[],
  { timeToExpire = 60 * 60 * 8 } = {},
) => {
  const cacheKey = agentCallSignature(agent, userPromptArgs);

  const cachedResult = await redis.get<string>(cacheKey);
  if (cachedResult) {
    logger.info(`Agent ${agent.name} cache hit`, { cacheKey, cachedResult });
    return JSON.parse(cachedResult);
  }

  const result = await callAgent(agent, userPromptArgs);
  await redis.set(cacheKey, JSON.stringify(result), timeToExpire);
  return result;
};
export { generateEmbedding, callFunction, callAgent, callAgentWithCache };
