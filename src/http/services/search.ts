import _ from 'lodash';
import { kysely } from '../../common/utils/pgConn.js';
import * as searchService from '../../common/services/searchService.js';
import * as config from '../../common/services/searchService/config.js';
import { toDatabaseSchema } from '../../common/services/searchService/utils.js';
import logger from '../../common/utils/logger.js';
import { Insight, RevenueTicket } from '../../types.js';

export const indexInsightForSearch = async (insight: Insight): Promise<void> => {
  await searchService.indexInsightsForSearch([insight]);
};

export const updateSearchIndex = async function (
  insightId: string,
  updates: Record<string, unknown>,
): Promise<void> {
  const ticketUpdates = toDatabaseSchema(updates);
  const embeddingUpdates = _.pick(updates, config.embeddingFields);

  logger.info('Updating search index', { insightId, updates, ticketUpdates, embeddingUpdates });

  let result: { id: string };
  if (_.isEmpty(_.omitBy(ticketUpdates, _.isUndefined))) {
    const queryResult = await kysely
      .selectFrom('revenue_ticket')
      .select(['id'])
      .where('insightId', '=', insightId)
      .executeTakeFirst();
    result = { id: queryResult?.id as string };
  } else {
    const [queryResult] = await kysely
      .updateTable('revenue_ticket')
      .set(ticketUpdates as Partial<RevenueTicket>)
      .where('insightId', '=', insightId)
      .returning(['id'])
      .execute();
    result = { id: queryResult.id as string };
  }

  await Promise.all(
    Object.entries(embeddingUpdates).map(([field, value]) =>
      searchService.updateEmbedding(result.id, field, value as string),
    ),
  );
};

export const deleteSearchIndex = async function (
  insightId: string,
): Promise<{ id: string } | undefined> {
  logger.info('Deleting insight from search index', { insightId });

  const [queryResult] = await kysely
    .deleteFrom('revenue_ticket')
    .where('insightId', '=', insightId)
    .returning(['id'])
    .execute();

  if (!queryResult) {
    logger.warn('No search index found to delete', { insightId });
    return;
  }

  logger.info('Successfully deleted insight from search index', { insightId });

  return { id: queryResult.id as string };
};
