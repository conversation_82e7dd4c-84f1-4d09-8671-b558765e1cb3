import { kysely } from '../../common/utils/pgConn.js';
import { validateAndStoreInsightsTrx } from '../../common/services/insightsService.js';
import logger from '../../common/utils/logger.js';
import { Compilable, sql } from 'kysely';
import { Ajv } from 'ajv';
import _addFormats from 'ajv-formats'; // Workaround - see https://github.com/ajv-validator/ajv-formats/issues/85#issuecomment-2262652443
const addFormats = _addFormats as unknown as typeof _addFormats.default;
import { validateAndPrepareInsight } from '../../common/services/insightsService.js';
import { Insight, SchemaNode } from '../../types.js';

import SCHEMA from '../../../contracts/airflow_insight_v1.json' with { type: 'json' };
import UPDATE_SCHEMA from '../../../contracts/insight_update_v1.json' with { type: 'json' };
const updateSchemaDeepCopy = JSON.parse(JSON.stringify(UPDATE_SCHEMA));

function logQuery(query: Compilable<unknown>, func: string): void {
  const rawSql = query.compile();
  logger.info(`Raw SQL query for ${func}:`, {
    sql: rawSql.sql,
    parameters: rawSql.parameters,
  });
}

interface InsightQueryParams {
  skip?: number;
  limit?: number;
}

interface InsightFilters {
  uuids?: string[];
  visible?: boolean;
  sensitivity?: string[];
  startDate?: Date;
  endDate?: Date;
  isTotal?: boolean;
  isSummary?: boolean;
  granularity?: string;
  breakdownKey?: string;
  breakdownValue?: string;
}

interface GetInsightsFilters extends InsightFilters {
  insightIds?: string[];
  orgIds?: string[];
}

const getOrganizationInsights = async (
  orgId: string,
  { skip, limit }: InsightQueryParams,
  filters: InsightFilters,
): Promise<Insight[]> => {
  try {
    let query = kysely
      .selectFrom('insights')
      .selectAll()
      .where('orgId', '=', orgId)
      .orderBy('incident_time_stamp', 'desc')
      .orderBy('displayId', 'desc') // to make ordering of insights with the exact same timestamp deterministic
      .limit(limit ?? 1000); // safety net

    if (skip !== undefined) query = query.offset(skip);
    if (filters.uuids !== undefined) query = query.where('id', 'in', filters.uuids);
    if (filters.visible !== undefined) query = query.where('visible', '=', filters.visible);
    if (filters.sensitivity !== undefined)
      query = query.where('sensitivity', 'in', filters.sensitivity);
    if (filters.startDate !== undefined)
      query = query.where('incidentTimeStamp', '>=', filters.startDate);
    if (filters.endDate !== undefined)
      query = query.where('incidentTimeStamp', '<=', filters.endDate);
    if (filters.isTotal !== undefined) query = query.where('isTotal', '=', filters.isTotal);
    if (filters.isSummary !== undefined) query = query.where('isSummary', '=', filters.isSummary);
    if (filters.granularity !== undefined)
      query = query.where('granularity', '=', filters.granularity);
    if (filters.breakdownKey !== undefined || filters.breakdownValue !== undefined)
      query = query.where(
        'breakdownDimension',
        '@>',
        sql`CAST(${JSON.stringify({ key: filters.breakdownKey, value: filters.breakdownValue })} AS JSONB)`,
      );

    logQuery(query, 'getOrganizationInsights');
    const result = await query.execute();
    return result;
  } catch (error) {
    logger.error('Error fetching insights:', error);
    throw error;
  }
};

const getOrganizationInsightsCount = async (
  orgId: string,
  filters: InsightFilters,
): Promise<number> => {
  try {
    let query = kysely
      .selectFrom('insights')
      .select(eb => eb.fn.countAll().as('count'))
      .where('orgId', '=', orgId);

    if (filters.visible !== undefined) query = query.where('visible', '=', filters.visible);
    if (filters.sensitivity !== undefined)
      query = query.where('sensitivity', 'in', filters.sensitivity);
    if (filters.startDate !== undefined)
      query = query.where('incidentTimeStamp', '>=', filters.startDate);
    if (filters.endDate !== undefined)
      query = query.where('incidentTimeStamp', '<=', filters.endDate);
    if (filters.isTotal !== undefined) query = query.where('isTotal', '=', filters.isTotal);
    if (filters.isSummary !== undefined) query = query.where('isSummary', '=', filters.isSummary);
    if (filters.granularity !== undefined)
      query = query.where('granularity', '=', filters.granularity);
    if (filters.breakdownKey !== undefined || filters.breakdownValue !== undefined)
      query = query.where(
        'breakdownDimension',
        '@>',
        sql`CAST(${JSON.stringify({ key: filters.breakdownKey, value: filters.breakdownValue })} AS JSONB)`,
      );

    logQuery(query, 'getOrganizationInsightsCount');
    const result = await query.executeTakeFirst();
    return parseInt(result?.count as string);
  } catch (error) {
    logger.error('Error counting insights:', error);
    throw error;
  }
};

const getInsights = async (
  { skip, limit }: InsightQueryParams,
  filters: GetInsightsFilters,
): Promise<Insight[]> => {
  try {
    let query = kysely
      .selectFrom('insights')
      .selectAll()
      .orderBy('incidentTimeStamp', 'desc')
      .orderBy('displayId', 'desc')
      .limit(limit ?? 1000); // safety net

    if (skip !== undefined) query = query.offset(skip);
    if (Array.isArray(filters.insightIds) && filters.insightIds.length > 0)
      query = query.where('displayId', 'in', filters.insightIds);
    if (Array.isArray(filters.uuids) && filters.uuids.length > 0)
      query = query.where('id', 'in', filters.uuids);
    if (Array.isArray(filters.orgIds) && filters.orgIds.length > 0)
      query = query.where('orgId', 'in', filters.orgIds);
    if (filters.startDate !== undefined)
      query = query.where('incidentTimeStamp', '>=', filters.startDate);
    if (filters.endDate !== undefined)
      query = query.where('incidentTimeStamp', '<=', filters.endDate);
    if (filters.isTotal !== undefined) query = query.where('isTotal', '=', filters.isTotal);
    if (filters.isSummary !== undefined) query = query.where('isSummary', '=', filters.isSummary);
    if (filters.granularity !== undefined)
      query = query.where('granularity', '=', filters.granularity);
    if (filters.breakdownKey !== undefined || filters.breakdownValue !== undefined)
      query = query.where(
        'breakdownDimension',
        '@>',
        sql`CAST(${JSON.stringify({ key: filters.breakdownKey, value: filters.breakdownValue })} AS JSONB)`,
      );

    logQuery(query, 'getInsights');
    const result = await query.execute();
    return result;
  } catch (error) {
    logger.error('Error fetching insights:', {
      error,
      params: { skip, limit },
      filters,
    });
    throw new Error(error as string);
  }
};

const getInsight = async (insightId: string, userOrgIds: string[]): Promise<Insight | null> => {
  try {
    const query = kysely
      .selectFrom('insights')
      .selectAll()
      .where('displayId', '=', insightId)
      .where('orgId', 'in', userOrgIds);
    logQuery(query, 'getInsight');
    const result = await query.executeTakeFirst();
    return result || null;
  } catch (error) {
    logger.error('Error fetching insight:', { error, insightId });
    throw error;
  }
};

const createInsight = async (insight: Insight): Promise<Insight> => {
  const result = await kysely.transaction().execute(async trx => {
    return validateAndStoreInsightsTrx([insight], trx);
  });
  logger.info(`All insights successfully inserted into the database.`);
  return result?.[0];
};

function rewriteReferences(obj: SchemaNode, baseSchema: SchemaNode): SchemaNode {
  if (typeof obj !== 'object' || obj === null) return obj;

  for (const key in obj) {
    const value = obj[key];
    if (value && typeof value === 'object' && '$ref' in value) {
      const refValue = (value as { $ref: string }).$ref;
      // Handle references to Insight properties
      if (refValue && refValue.includes('#/components/schemas/Insight/properties/')) {
        const propertyName = refValue.split('/').pop();
        if (propertyName && baseSchema && 'properties' in baseSchema) {
          obj[key] = (baseSchema.properties as SchemaNode)[propertyName] || value;
        }
      }
    }

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      rewriteReferences(value, baseSchema);
    }
  }

  for (const key in obj) {
    const value = obj[key];
    if (value && typeof value === 'object' && '$ref' in value) {
      const refValue = (value as { $ref: string }).$ref;
      // Handle references to Insight definitions that appear after handling references to Insight properties
      if (refValue && refValue.includes('#/components/schemas/Insight/definitions/')) {
        const definitionName = refValue.split('/').pop();
        if (definitionName && baseSchema && 'definitions' in baseSchema) {
          obj[key] = (baseSchema.definitions as SchemaNode)[definitionName] || value;
        }
      }
    }

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      rewriteReferences(value, baseSchema);
    }
  }
  return obj;
}

const updateInsight = async (
  insightId: string,
  updateData: Insight,
  orgId: string,
): Promise<Insight | undefined> => {
  // Use deep copy of schema because Node.js caches require statements,
  // so changing the original UPDATE_SCHEMA variable would impact Swagger docs
  rewriteReferences(updateSchemaDeepCopy, SCHEMA);

  // validate patched insight schema
  const ajv = new Ajv({
    // Add support for references
    $data: true,
    allowUnionTypes: true,
  });
  addFormats(ajv);
  const validate = ajv.compile(updateSchemaDeepCopy);

  const preparedUpdateData = validateAndPrepareInsight(updateData, validate);

  try {
    const query = kysely
      .updateTable('insights')
      .set({
        ...preparedUpdateData,
        updated_at: sql`now()`,
      })
      .where('display_id', '=', insightId)
      .where('orgId', '=', orgId)
      .returningAll();

    logQuery(query, 'updateInsight');
    const result = await query.executeTakeFirst();
    return result;
  } catch (error) {
    logger.error('Error updating insight:', error, insightId);
    throw new Error((error as Error).message, { cause: 'database-error' });
  }
};

const deleteInsight = async (insightId: string, orgId: string): Promise<string | null> => {
  try {
    const query = kysely
      .deleteFrom('insights')
      .where('displayId', '=', insightId)
      .where('orgId', '=', orgId)
      .returning('displayId');

    logQuery(query, 'deleteInsight');
    const result = await query.executeTakeFirst();

    if (!result || typeof result.displayId !== 'string') {
      logger.warn('Insight not found for deletion', { insightId });
      return null;
    }

    logger.info('Insight deleted successfully', { insightId });
    return result.displayId;
  } catch (error) {
    logger.error('Error deleting insight:', { error, insightId });
    throw new Error('Failed to delete insight', { cause: 'database-error' });
  }
};

export {
  getOrganizationInsights,
  getInsights,
  getInsight,
  createInsight,
  updateInsight,
  deleteInsight,
  getOrganizationInsightsCount,
};
