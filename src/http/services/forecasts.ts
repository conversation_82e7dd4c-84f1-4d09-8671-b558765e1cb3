import { kysely } from '../../common/utils/pgConn.js';
import logger from '../../common/utils/logger.js';
import { Compilable } from 'kysely';
import { Ajv } from 'ajv';
import _addFormats from 'ajv-formats'; // Workaround - see https://github.com/ajv-validator/ajv-formats/issues/85#issuecomment-2262652443
const addFormats = _addFormats as unknown as typeof _addFormats.default;
import { RevenueForecastReport } from '../../types.js';
import { forecastSchemaApi } from '../../../contracts/revenueForecast.js';

const ajv = new Ajv({
  // Add support for references
  $data: true,
  allowUnionTypes: true,
});
addFormats(ajv);

function logQuery(query: Compilable<unknown>, func: string): void {
  const rawSql = query.compile();
  logger.info(`Raw SQL query for ${func}:`, {
    sql: rawSql.sql,
    parameters: rawSql.parameters,
  });
}

export const getOrganizationForecasts = async (orgId: string): Promise<RevenueForecastReport[]> => {
  try {
    const query = kysely
      .selectFrom('revenue_forecast_reports')
      .select(['id', 'periodStart'])
      .where('orgId', '=', orgId)
      .orderBy('periodStart', 'desc');

    logQuery(query, 'getOrganizationForecasts');
    const result = await query.execute();
    return result;
  } catch (error) {
    logger.error('Error fetching forecasts:', error);
    throw error;
  }
};

export const getOrganizationForecast = async (
  id: string,
  userOrgIds: string[],
): Promise<RevenueForecastReport> => {
  try {
    const query = kysely
      .selectFrom('revenue_forecast_reports')
      .selectAll()
      .where('id', '=', id)
      .where('orgId', 'in', userOrgIds)
      .orderBy('periodStart', 'desc');

    logQuery(query, 'getOrganizationForecast');
    const result = await query.execute();
    return result[0];
  } catch (error) {
    logger.error('Error fetching forecast:', error);
    throw error;
  }
};

export const getLatestOrganizationForecast = async (
  orgId: string,
): Promise<RevenueForecastReport> => {
  try {
    const query = kysely
      .selectFrom('revenue_forecast_reports')
      .selectAll()
      .where('orgId', '=', orgId)
      .limit(1)
      .orderBy('periodStart', 'desc')
      .orderBy('id', 'asc'); // if two reports for the same date, get the latest

    logQuery(query, 'getLatestOrganizationForecast');
    const result = await query.execute();
    return result[0];
  } catch (error) {
    logger.error('Error fetching forecast:', error);
    throw error;
  }
};

import { convertStringToCamelCase } from '@/common/utils/jsonSchema.js';

const convertRootKeysToCamelCase = (obj: RevenueForecastReport): void => {
  const entries = Object.entries(obj);
  entries.forEach(([key, value]) => {
    const camelKey = convertStringToCamelCase(key);
    if (key !== camelKey) {
      delete obj[key];
      obj[camelKey] = value;
    }
  });
};

export const createForecast = async (
  forecast: RevenueForecastReport,
): Promise<RevenueForecastReport> => {
  convertRootKeysToCamelCase(forecast);
  const validate = ajv.compile(forecastSchemaApi);
  const isValid = validate(forecast);
  const errors = validate.errors;

  if (!isValid) {
    const errorMessage = `Forecast failed to pass validation: ${ajv.errorsText(errors)}`;
    logger.error(errorMessage, { forecast, errors });
    throw new Error(errorMessage, { cause: 'malformed-request' });
  }

  try {
    const preparedForecast = {
      ...forecast,
      revenueGroups: JSON.stringify(forecast.revenueGroups),
      keyMetric: JSON.stringify(forecast.keyMetric),
    };

    logger.info('preparedForecast:', forecast);

    const insertedForecasts = (await kysely
      .insertInto('revenue_forecast_reports')
      .values(preparedForecast)
      .returningAll()
      .execute()) as unknown as RevenueForecastReport[];

    return insertedForecasts?.[0];
  } catch (error) {
    logger.error('Failed to insert forecast', { forecast, error });
    throw new Error('Failed to insert forecast');
  }
};
