import { sql, SelectQueryBuilder } from 'kysely';
import { UserInteraction, UserInteractionLatestSummary } from '../../types.js';
import logger from '../../common/utils/logger.js';
import { kysely } from '../../common/utils/pgConn.js';

interface DateRange {
  dateFrom?: string;
  dateTo?: string;
}

interface PaginationParams {
  skip?: number;
  limit?: number;
}

interface FilterParams {
  userIds?: string[];
  insightIds?: string[];
  orgIds?: string[];
  startDate?: string;
  endDate?: string;
}

const actionTypes = {
  AI_SUGGESTIONS_CLICK: 'ai-suggestions-click',
  HYPOTHESIS_CLICK: 'hypothesis-click',
  HYPOTHESIS_FEEDBACK_SUBMISSION: 'hypothesis-feedback-submission',
} as const;

function logQuery(
  query: SelectQueryBuilder<Record<string, unknown>, string, Record<string, unknown>>,
  func: string,
): void {
  const rawSql = query.compile();
  logger.info(`Raw SQL query for ${func}:`, {
    sql: rawSql.sql,
    parameters: rawSql.parameters,
  });
}

export const getAISuggestionsSummary = async (
  insightId: string,
  orgId: string,
): Promise<UserInteractionLatestSummary[]> => {
  const result = await kysely
    .selectFrom('user_interactions_latest_summary')
    .select(['actionTargetGroup', 'actionTargetId', 'actionValue', 'interactionCount'])
    .where('orgId', '=', orgId)
    .where('actionType', '=', actionTypes.AI_SUGGESTIONS_CLICK)
    .where('insightId', '=', insightId)
    .execute();

  return result as UserInteractionLatestSummary[];
};

export const getAISuggestionsLatest = async (
  insightId: string,
  userId: string | undefined,
  orgId: string,
): Promise<UserInteraction[]> => {
  let query = kysely
    .selectFrom('user_interactions_latest')
    .select(['userId', 'actionTargetGroup', 'actionTargetId', 'actionValue'])
    .where('orgId', '=', orgId)
    .where('actionType', '=', actionTypes.AI_SUGGESTIONS_CLICK)
    .where('insightId', '=', insightId);

  if (userId) {
    query = query.where('userId', '=', userId);
  }

  const result = await query.execute();
  return result as UserInteraction[];
};

export const getAISuggestionsLatestByDate = async (
  { dateFrom, dateTo }: DateRange,
  orgId: string,
): Promise<UserInteraction[]> => {
  let query = kysely
    .selectFrom('user_interactions_latest')
    .select([
      'timestamp',
      'userId',
      'insightId',
      'actionType',
      'actionTargetId',
      'actionTargetGroup',
      'actionValue',
    ])
    .where('orgId', '=', orgId)
    .where('actionType', '=', actionTypes.AI_SUGGESTIONS_CLICK);

  if (dateFrom) {
    query = query.where('timestamp', '>=', sql`${dateFrom}::timestamp`);
  }
  if (dateTo) {
    query = query.where('timestamp', '<=', sql`${dateTo}::timestamp + INTERVAL '1 day'`);
  }

  const result = await query.orderBy('timestamp', 'desc').execute();
  return result as UserInteraction[];
};

const createUserInteraction = async (
  insightId: string,
  actionType: string,
  interaction: Partial<UserInteraction>,
): Promise<UserInteraction> => {
  const result = await kysely
    .insertInto('user_interactions')
    .values({
      insightId,
      actionType,
      ...interaction,
    })
    .returningAll()
    .executeTakeFirst();

  return result as UserInteraction;
};

export const createAISuggestionInteraction = async (
  insightId: string,
  interaction: Partial<UserInteraction>,
): Promise<UserInteraction> =>
  createUserInteraction(insightId, actionTypes.AI_SUGGESTIONS_CLICK, interaction);

export const createHypothesisClick = async (
  insightId: string,
  interaction: Partial<UserInteraction>,
): Promise<UserInteraction> =>
  createUserInteraction(insightId, actionTypes.HYPOTHESIS_CLICK, interaction);

export const createHypothesisFeedback = async (
  insightId: string,
  interaction: Partial<UserInteraction>,
): Promise<UserInteraction> =>
  createUserInteraction(insightId, actionTypes.HYPOTHESIS_FEEDBACK_SUBMISSION, interaction);

export type ExtendedUserInteraction = UserInteraction & {
  action_target_value: string;
};

export const getAISuggestionsLatestAll = async function (
  { skip, limit }: PaginationParams,
  { userIds, insightIds, orgIds, startDate, endDate }: FilterParams,
): Promise<ExtendedUserInteraction[]> {
  let query = kysely
    .selectFrom('user_interactions_latest as u')
    .innerJoin('insights as i', join => join.on(sql`u.insight_id::integer = i.display_id`))
    .selectAll('u')
    .select([
      sql<string>`
        jsonb_extract_path_text(
          i.ai_actions::jsonb,
          u.action_target_group,
          (
            SELECT (array_position(
              ARRAY(
                SELECT jsonb_array_elements(
                  (i.ai_actions::jsonb)->u.action_target_group
                )->>'id'
              )::int[],
              u.action_target_id::int
            ) - 1)::text
          ),
          'action'
        )
      `.as('action_target_value'),
    ])
    .where('u.action_type', '=', actionTypes.AI_SUGGESTIONS_CLICK)
    .orderBy('u.timestamp', 'desc')
    .limit(limit ?? 1000);

  if (skip !== undefined) query = query.offset(skip);
  if (userIds !== undefined) query = query.where('u.user_id', 'in', userIds);
  if (insightIds !== undefined) query = query.where('u.insight_id', 'in', insightIds);
  if (orgIds !== undefined) query = query.where('u.org_id', 'in', orgIds);
  if (startDate !== undefined)
    query = query.where('u.timestamp', '>=', sql`${startDate}::timestamp`);
  if (endDate !== undefined)
    query = query.where('u.timestamp', '<=', sql`${endDate}::timestamp + INTERVAL '1 day'`);

  logQuery(query, 'getAISuggestionsLatestAll');

  const result = await query.execute();
  return result as ExtendedUserInteraction[];
};
