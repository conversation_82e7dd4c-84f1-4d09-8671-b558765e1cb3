import express from 'express';

import userInteractionsRoutes from './userInteractions.js';
import insightsRoutes from './insights.js';
import organizationRoutes from './organization.js';
import forecastRoutes from './forecasts.js';

const router = express.Router();

router.use('/insights', userInteractionsRoutes);
router.use('/insights', insightsRoutes);
router.use('/organizations', organizationRoutes);
router.use('/forecasts', forecastRoutes);

export default router;
