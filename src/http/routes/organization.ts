import express from 'express';
import * as organizationsController from '../controllers/organizationsController.js';
import * as searchController from '../controllers/searchController.js';
import { checkPermissions } from '../middleware/permissions.middleware.js';
import { Permission } from '../../permission.js';

const router = express.Router({ mergeParams: true });

/**
 * @openapi
 * /api/v1/organizations/{orgId}/insights:
 *   get:
 *     summary: Get insights for an organization
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *         description: Number of items to skip
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of items to return. defaults to 1000
 *       - in: query
 *         name: uuids
 *         schema:
 *           type: string
 *         description: Comma-separated list of insight UUIDs to filter by
 *       - in: query
 *         name: visible
 *         schema:
 *           type: boolean
 *         description: Filter by visibility
 *       - in: query
 *         name: sensitivity
 *         schema:
 *           type: string
 *         description: Comma-separated list of sensitivity levels
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter insights after this date
 *         example: "2024-01-01"
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter insights before this date
 *         example: "2024-03-15"
 *       - in: query
 *         name: isTotal
 *         schema:
 *           type: boolean
 *         description: Filter by total flag
 *       - in: query
 *         name: isSummary
 *         schema:
 *           type: boolean
 *         description: Filter by summary flag
 *       - in: query
 *         name: granularity
 *         schema:
 *           type: string
 *         description: Filter by granularity level
 *       - in: query
 *         name: breakdownKey
 *         schema:
 *           type: string
 *         description: Filter by breakdown key. Can be used alone or combined with breakdown value
 *       - in: query
 *         name: breakdownValue
 *         schema:
 *           type: string
 *         description: Filter by breakdown value. Can be used alone or combined with breakdown key
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 insights:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Insight'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/insights',
  checkPermissions([Permission.ReadInsights]),
  organizationsController.getOrganizationInsights,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/insights/count:
 *   get:
 *     summary: Get count of insights for an organization
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: query
 *         name: visible
 *         schema:
 *           type: boolean
 *         description: Filter by visibility
 *       - in: query
 *         name: sensitivity
 *         schema:
 *           type: string
 *         description: Filter by sensitivity levels (comma-separated)
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter insights after this date
 *         example: "2024-03-15"
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter insights before this date
 *         example: "2024-03-15"
 *       - in: query
 *         name: isTotal
 *         schema:
 *           type: boolean
 *         description: Filter by total flag
 *       - in: query
 *         name: isSummary
 *         schema:
 *           type: boolean
 *         description: Filter by summary flag
 *       - in: query
 *         name: granularity
 *         schema:
 *           type: string
 *         description: Filter by granularity level
 *       - in: query
 *         name: breakdownKey
 *         schema:
 *           type: string
 *         description: Filter by breakdown key. Can be used alone or combined with breakdown value
 *       - in: query
 *         name: breakdownValue
 *         schema:
 *           type: string
 *         description: Filter by breakdown value. Can be used alone or combined with breakdown key
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 filteredCount:
 *                   type: integer
 *                   description: Number of insights matching the filters
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/insights/count',
  checkPermissions([Permission.ReadInsights]),
  organizationsController.getOrganizationInsightsCount,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/insights/search:
 *   get:
 *     summary: Search insights within an organization
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 1
 *         description: Page number for pagination
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 insights:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Insight'
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of matching results
 *                 filteredCount:
 *                   type: integer
 *                   description: Number of results after applying filters
 *                 currentFilters:
 *                   type: object
 *                   description: Currently applied filters
 *       422:
 *         description: Bad request - invalid input parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/insights/search',
  checkPermissions([Permission.ReadInsights]),
  searchController.search,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/user_interactions/ai_suggestions/latest:
 *   get:
 *     summary: Get the latest AI Suggestion interactions for an organization
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter user interactions after this date
 *         example: "2024-01-01"
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter user interactions before this date
 *         example: "2024-03-15"
 *     responses:
 *       200:
 *         description: User interactions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 aiSuggestions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-11-26T10:31:01.506Z"
 *                       userId:
 *                         type: string
 *                         example: "auth0|123a4eeb456789123fe4be5f"
 *                       insightId:
 *                         type: string
 *                         example: "69337"
 *                       actionType:
 *                         type: string
 *                         example: "ai-suggestions-click"
 *                       actionTargetId:
 *                         type: string
 *                         example: "1"
 *                       actionTargetGroup:
 *                         type: string
 *                         example: "immediate"
 *                       actionValue:
 *                         type: string
 *                         example: "dislike"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/user_interactions/ai_suggestions/latest',
  checkPermissions([Permission.ReadUserInteractions]),
  organizationsController.getAISuggestionsLatestByDate,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/insights/search_v4:
 *   get:
 *     summary: Search insights within an organization using v4 of the algorithm
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 1
 *         description: Page number for pagination
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 insights:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Insight'
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of matching results
 *                 filteredCount:
 *                   type: integer
 *                   description: Number of results after applying filters
 *                 currentFilters:
 *                   type: object
 *                   description: Currently applied filters
 *       422:
 *         description: Bad request - invalid input parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/insights/search_v4',
  checkPermissions([Permission.ReadInsights]),
  searchController.search_v4,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/forecasts:
 *   get:
 *     summary: Get a list of revenue forecasts for an organization
 *     tags: [Revenue Forecast reports]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *     responses:
 *       200:
 *         description: List of revenue forecasts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 forecasts:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                         title: Unique identifier
 *                       periodStart:
 *                         type: string
 *                         format: date
 *                         title: Starting date of the report period
 *       401:
 *         description: Unauthorized request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/forecasts/',
  checkPermissions([Permission.ReadRevenueForecasts]),
  organizationsController.getOrganizationForecasts,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/forecasts/latest:
 *   get:
 *     summary: Get the latest revenue forecast for an organization
 *     tags: [Revenue Forecast reports]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *     responses:
 *       200:
 *         description: Latest revenue forecast
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RevenueForecastReport'
 *       401:
 *         description: Unauthorized request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       404:
 *         description: No forecasts found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/forecasts/latest',
  checkPermissions([Permission.ReadRevenueForecasts]),
  organizationsController.getLatestOrganizationForecast,
);

/**
 * @openapi
 * /api/v1/organizations/{orgId}/forecasts/{forecastId}:
 *   get:
 *     summary: Get a specific revenue forecast by ID
 *     tags: [Revenue Forecast reports]
 *     parameters:
 *       - in: path
 *         name: orgId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Auth0 ID of the organization
 *       - in: path
 *         name: forecastId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the forecast to retrieve
 *     responses:
 *       200:
 *         description: Revenue forecast details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RevenueForecastReport'
 *       401:
 *         description: Unauthorized request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       404:
 *         description: Forecast not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       409:
 *         description: Wrong organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 correctOrg:
 *                   type: object
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:orgId/forecasts/:forecastId',
  checkPermissions([Permission.ReadRevenueForecasts]),
  organizationsController.getOrganizationForecast,
);

export default router;
