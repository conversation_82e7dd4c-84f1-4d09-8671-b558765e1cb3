import express from 'express';
import * as userInteractionsController from '../controllers/userInteractionsController.js';
import { checkPermissions } from '../middleware/permissions.middleware.js';
import { Permission } from '../../permission.js';

const router = express.Router();

/**
 * @openapi
 * /api/v1/insights/{insightId}/user_interactions/ai_suggestions/summary:
 *   get:
 *     summary: Get the summary of AI Suggestion counters for user interactions of an insight
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the insight
 *     responses:
 *       200:
 *         description: AI Suggestions counters summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 aiSuggestionsSummary:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       actionTargetGroup:
 *                         type: string
 *                         example: "immediate"
 *                       actionTargetId:
 *                         type: string
 *                         example: "1"
 *                       actionValue:
 *                         type: string
 *                         example: "dislike"
 *                       interactionCount:
 *                         type: integer
 *                         example: 1
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:insightId/user_interactions/ai_suggestions/summary',
  checkPermissions([Permission.ReadUserInteractions]),
  userInteractionsController.getAISuggestionsSummary,
);

/**
 * @openapi
 * /api/v1/insights/{insightId}/user_interactions/ai_suggestions/latest:
 *   get:
 *     summary: Get the latest AI Suggestion interactions for an insight
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the insight
 *       - in: query
 *         name: userId
 *         required: false
 *         schema:
 *           type: string
 *         description: Optional user ID to filter interactions for a specific user
 *     responses:
 *       200:
 *         description: Latest AI Suggestion interactions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 aiSuggestions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: "google-oauth2|123456789123456789123"
 *                       actionTargetGroup:
 *                         type: string
 *                         example: "immediate"
 *                       actionTargetId:
 *                         type: string
 *                         example: "1"
 *                       actionValue:
 *                         type: string
 *                         example: "dislike"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:insightId/user_interactions/ai_suggestions/latest',
  checkPermissions([Permission.ReadUserInteractions]),
  userInteractionsController.getAISuggestionsLatest,
);

/**
 * @openapi
 * /api/v1/insights/{insightId}/user_interactions/ai_suggestions:
 *   post:
 *     summary: Create a new AI Suggestion user interaction
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The unique ID of the insight
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - actionValue
 *               - actionTargetId
 *               - actionTargetGroup
 *             properties:
 *               actionValue:
 *                 type: string
 *                 description: The value of the user interaction
 *                 example: "dislike"
 *                 enum: ["like", "dislike"]
 *               actionTargetId:
 *                 type: string
 *                 description: The identifier of the target action
 *                 example: "1"
 *               actionTargetGroup:
 *                 type: string
 *                 description: The group of the target action
 *                 example: "immediate"
 *     responses:
 *       201:
 *         description: Successfully created AI suggestion interaction
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   example: "google-oauth2|105359217596734648118"
 *                 actionTargetGroup:
 *                   type: string
 *                   example: "immediate"
 *                 actionTargetId:
 *                   type: string
 *                   example: "1"
 *                 actionValue:
 *                   type: string
 *                   example: "dislike"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  '/:insightId/user_interactions/ai_suggestions',
  checkPermissions([Permission.UpdateUserInteractions]),
  userInteractionsController.createAISuggestionInteraction,
);

/**
 * @openapi
 * /api/v1/insights/{insightId}/user_interactions/hypothesis_click:
 *   post:
 *     summary: Create a new Hypothesis Click user interaction
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The unique ID of the insight
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - actionValue
 *               - actionTargetId
 *             properties:
 *               actionValue:
 *                 type: string
 *                 description: The value of the user interaction
 *                 example: "UX issues: website errors"
 *               actionTargetId:
 *                 type: string
 *                 description: The identifier of the action target
 *                 example: "channel-performance-differences"
 *     responses:
 *       201:
 *         description: Successfully created AI suggestion interaction
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   example: "google-oauth2|105359217596734648118"
 *                 actionTargetGroup:
 *                   type: string
 *                   example: "immediate"
 *                 actionTargetId:
 *                   type: string
 *                   example: "1"
 *                 actionValue:
 *                   type: string
 *                   example: "dislike"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  '/:insightId/user_interactions/hypothesis_click',
  checkPermissions([Permission.UpdateUserInteractions]),
  userInteractionsController.createHypothesisClick,
);

/**
 * @openapi
 * /api/v1/insights/{insightId}/user_interactions/hypothesis_feedback:
 *   post:
 *     summary: Create a new Hypothesis Feedback user interaction
 *     tags: [User interactions]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The unique ID of the insight
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - actionValue
 *             properties:
 *               actionValue:
 *                 type: string
 *                 description: The value of the user interaction
 *                 example: "Error clicking the clickety click"
 *     responses:
 *       201:
 *         description: Successfully created AI suggestion interaction
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   example: "google-oauth2|105359217596734648118"
 *                 actionTargetGroup:
 *                   type: string
 *                   example: "immediate"
 *                 actionTargetId:
 *                   type: string
 *                   example: "1"
 *                 actionValue:
 *                   type: string
 *                   example: "dislike"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  '/:insightId/user_interactions/hypothesis_feedback',
  checkPermissions([Permission.UpdateUserInteractions]),
  userInteractionsController.createHypothesisFeedback,
);

/**
 * @openapi
 * /api/v1/insights/user_interactions/ai_suggestions/latest:
 *   get:
 *     summary: Get the latest AI Suggestion interactions across all insights
 *     tags: [User interactions]
 *     parameters:
 *       - in: query
 *         name: skip
 *         required: false
 *         schema:
 *           type: integer
 *         description: Number of records to skip for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1000
 *         description: Maximum number of records to return
 *       - in: query
 *         name: userIds
 *         required: false
 *         schema:
 *           type: string
 *         description: Comma-separated list of user IDs to filter interactions
 *       - in: query
 *         name: insightIds
 *         required: false
 *         schema:
 *           type: string
 *         description: Comma-separated list of insight IDs to filter interactions
 *       - in: query
 *         name: orgIds
 *         required: false
 *         schema:
 *           type: string
 *         description: Comma-separated list of organization IDs to filter interactions
 *       - in: query
 *         name: startDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering interactions
 *       - in: query
 *         name: endDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering interactions
 *     responses:
 *       200:
 *         description: Latest AI Suggestion interactions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 aiSuggestions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: "google-oauth2|123456789123456789123"
 *                       orgId:
 *                         type: string
 *                         example: "org_asdfasdfaas"
 *                       insightId:
 *                         type: integer
 *                         example: "org_asdfasdfaas"
 *                       actionType:
 *                         type: string
 *                         example: "ai-suggestions-click"
 *                       actionTargetGroup:
 *                         type: string
 *                         example: "immediate"
 *                       actionTargetId:
 *                         type: string
 *                         example: "1"
 *                       actionValue:
 *                         type: string
 *                         example: "dislike"
 *                       timestamp:
 *                         type: string
 *                         example: "2025-01-15T12:34:56.789Z"
 *                         format: date-time
 *                       actionTargetValue:
 *                         type: string
 *                         example: "Review promotional calendar effectiveness"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */

router.get(
  '/user_interactions/ai_suggestions/latest',
  checkPermissions([Permission.ReadUserInteractionsAll]),
  userInteractionsController.getAISuggestionsLatestAll,
);

export default router;
