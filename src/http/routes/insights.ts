import express from 'express';
import * as insightsController from '../controllers/insightsController.js';
import { checkPermissions } from '../middleware/permissions.middleware.js';
import { Permission } from '../../permission.js';

const router = express.Router();

/**
 * @openapi
 * /api/v1/insights/{insightId}:
 *   get:
 *     summary: Retrieve a single insight
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the insight to retrieve
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insight'
 *       404:
 *         description: Insight not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       409:
 *         description: Insight belongs to a different organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 correctOrg:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     display_name:
 *                       type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get(
  '/:insightId',
  checkPermissions([Permission.ReadInsights]),
  insightsController.getInsight,
);

/**
 * @openapi
 * /api/v1/insights/{insightId}:
 *   put:
 *     summary: Update an existing insight
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the insight to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InsightUpdate'
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insight'
 *       400:
 *         description: Malformed update object
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 *       404:
 *         description: Insight not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.put(
  '/:insightId',
  checkPermissions([Permission.UpdateInsights]),
  insightsController.updateInsight,
);

/**
 * @openapi
 * /api/v1/insights:
 *   post:
 *     summary: Create a new insight
 *     tags: [Insights]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Insight'
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insight'
 *       400:
 *         description: Malformed create object
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.post('/', checkPermissions([Permission.UpdateInsights]), insightsController.createInsight);

/**
 * @openapi
 * /api/v1/insights/{insightId}:
 *   delete:
 *     summary: Delete an insight
 *     tags: [Insights]
 *     parameters:
 *       - in: path
 *         name: insightId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the insight to delete
 *     responses:
 *       204:
 *         description: Insight successfully deleted
 *       404:
 *         description: Insight not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.delete(
  '/:insightId',
  checkPermissions([Permission.UpdateInsights]),
  insightsController.deleteInsight,
);

/**
 * @openapi
 * /api/v1/insights:
 *   get:
 *     summary: Get insights with optional filters
 *     tags: [Insights]
 *     parameters:
 *       - in: query
 *         name: insightIds
 *         schema:
 *           type: string
 *         description: Comma-separated list of insight IDs
 *       - in: query
 *         name: uuids
 *         schema:
 *           type: string
 *         description: Comma-separated list of insight UUIDs
 *       - in: query
 *         name: orgIds
 *         schema:
 *           type: string
 *         description: Comma-separated list of organization IDs
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *         description: Number of records to skip for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 1000
 *         description: Maximum number of records to return
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering insights
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering insights
 *       - in: query
 *         name: isSummary
 *         schema:
 *           type: boolean
 *         description: Filter for summary insights
 *       - in: query
 *         name: isTotal
 *         schema:
 *           type: boolean
 *         description: Filter for total insights
 *       - in: query
 *         name: granularity
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly, yearly]
 *         description: Filter by granularity
 *       - in: query
 *         name: breakdownKey
 *         schema:
 *           type: string
 *         description: Key for breakdown dimension filtering
 *       - in: query
 *         name: breakdownValue
 *         schema:
 *           type: string
 *         description: Value for breakdown dimension filtering
 *     responses:
 *       200:
 *         description: Successfully retrieved insights
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 insights:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Insight'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.get('/', checkPermissions([Permission.ReadInsightsAll]), insightsController.getInsights);

export default router;
