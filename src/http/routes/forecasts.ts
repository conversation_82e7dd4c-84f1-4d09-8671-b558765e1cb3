import express from 'express';
import * as forecastsController from '../controllers/forecastsController.js';
import { checkPermissions } from '../middleware/permissions.middleware.js';
import { Permission } from '../../permission.js';

const router = express.Router();
/**
 * @openapi
 * /api/v1/forecasts:
 *   post:
 *     tags: [Revenue Forecast reports]
 *     summary: Create a new revenue forecast report
 *     description: Creates a new revenue forecast report with the provided data. Root keys can be provided both in pascal_case and camelCase.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RevenueForecastReport'
 *     responses:
 *       200:
 *         description: Forecast created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RevenueForecastReport'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Bad request
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 *                 message:
 *                   type: string
 */
router.post(
  '/',
  checkPermissions([Permission.UpdateRevenueForecasts]),
  forecastsController.createForecast,
);

export default router;
