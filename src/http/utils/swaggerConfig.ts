import swaggerJsdoc from 'swagger-jsdoc';
import Insight from '../../../contracts/airflow_insight_v1.json' with { type: 'json' };
import InsightUpdate from '../../../contracts/insight_update_v1.json' with { type: 'json' };
import { forecastSchemaApiDocs as RevenueForecastReport } from '../../../contracts/revenueForecast.js';

const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.3',
    info: {
      title: 'Insights App BE',
      version: '1.0.0',
      description:
        'Endpoints for the Insights App BE API.\n\nNotes that any mention of `id` or `insightId` refers to `displayId`',
    },
    tags: [
      {
        name: 'Info',
        description: 'Operations related to service information',
      },
      {
        name: 'Insights',
        description: 'Operations related to insights',
      },
      {
        name: 'User interactions',
        description: 'Operations related to FE user interactions',
      },
      {
        name: 'Revenue Forecast reports',
        description: 'Operations related to revenue forecast reports',
      },
    ],
    components: {
      schemas: {
        Insight,
        InsightUpdate,
        RevenueForecastReport,
      },
    },
  },
  apis: ['./src/**/*.js', './src/**/*.ts'],
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

export default swaggerSpec;
