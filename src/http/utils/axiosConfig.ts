import axios, { AxiosInstance, AxiosError } from 'axios';
import axiosRetry from 'axios-retry';

// Create a custom axios instance
const axiosInstance: AxiosInstance = axios.create();

// Configure axios-retry on the custom instance
axiosRetry(axiosInstance, {
  retries: 3, // Adjust based on your needs
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: (error: AxiosError): boolean => {
    if (error.response?.status) {
      // Retry on rate limit error (HTTP 429) or if it's a server error (HTTP 5xx)
      return error.response.status === 429 || error.response.status >= 500;
    }
    return false;
  },
});

export default axiosInstance;
