import 'dotenv/config';
import { checkJwt, extractTokenInfo } from './middleware/auth.middleware.js';
import routes from './routes/index.js';
import { errorHandler } from './middleware/error.middleware.js';
import { logApiCalls } from './middleware/logger.middleware.js';
import { compressionMiddleware } from './middleware/compression.middleware.js';

import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './utils/swaggerConfig.js';

import cors from 'cors';
import express, { Application } from 'express';
import bodyParser from 'body-parser';

const app: Application = express();

// CORS configuration
const corsOptions = {
  origin: [process.env.INSIGHTS_BFF_URL as string],
  optionsSuccessStatus: 200,
};

app.use(compressionMiddleware);
app.use(cors(corsOptions));

/**
 * @openapi
 * /status:
 *   get:
 *     summary: Retrieve the service of the status; either returns 200 or does not respond at all
 *     tags: [Info]
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
app.get('/status', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

/**
 * @openapi
 * /api/v1/docs:
 *   get:
 *     summary: Retrieve the OpenAPI documentation of the service
 *     tags: [Info]
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           text/html
 */
app.use('/api/v1/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.use(checkJwt);
app.use(extractTokenInfo);
app.use(logApiCalls);
app.use(bodyParser.json({ limit: '20mb' }));
app.use('/api/v1', routes);
app.use(errorHandler);

export default app;
