import { Response, NextFunction } from 'express';
import { auth } from 'express-oauth2-jwt-bearer';
import jwt from 'jsonwebtoken';
import { BaseApiRequest, AuthenticatedRequest } from '../../types.js';

export const checkJwt = auth({
  audience: [
    `${process.env.INSIGHTS_APP_API_AUTH0_AUDIENCE}`
  ],
  issuerBaseURL: `https://${process.env.AUTH0_DOMAIN}`,
});

export const extractTokenInfo = (
  req: BaseApiRequest,
  res: Response,
  next: NextFunction,
): Response | void => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.split(' ')[1];
    const decodedToken = jwt.decode(token) as {
      org_id?: string;
      sub?: string;
      [key: string]: unknown;
    } | null;

    if (!decodedToken) {
      throw new Error('Invalid token');
    }

    // Type assertion since we're adding required properties
    const authenticatedReq = req as AuthenticatedRequest;

    // Field req.user is required by multiple 3rd party libraries including express-jwt-authz
    if (authenticatedReq.auth) {
      authenticatedReq.user = authenticatedReq.auth.payload;
    }

    authenticatedReq.orgId = decodedToken.org_id || '';
    authenticatedReq.userEmail = decodedToken[
      `${process.env.AUTH0_CLAIMS_NAMESPACE}/email`
    ] as string;
    authenticatedReq.userId = decodedToken.sub || '';
    authenticatedReq.userOrgs = decodedToken[
      `${process.env.AUTH0_CLAIMS_NAMESPACE}/userOrgs`
    ] as string[];

    next();
  } catch {
    return res.status(400).send('Invalid token');
  }
};
