import { Response, NextFunction } from 'express';
import logger from '../../common/utils/logger.js';
import { BaseApiRequest } from '../../types.js';
import { CustomError } from '../../types.js';

export const errorHandler = (
  err: CustomError,
  _req: BaseApiRequest,
  res: Response,
  _next: NextFunction,
): void => {
  if (err.name === 'UnauthorizedError' || err.name === 'InvalidTokenError') {
    // Handle authentication errors
    logger.warn(`Invalid token | ${err.name || 'N/A'} | ${err.message || 'N/A'}`, { error: err });
    res.status(401).send('Invalid token');
  } else if (err.name === 'ForbiddenError' || err.message === 'Insufficient scope') {
    // Handle forbidden errors (e.g., missing scopes)
    logger.warn(`Insufficient scope | ${err.name || 'N/A'} | ${err.message || 'N/A'}`, {
      error: err,
    });
    res.status(403).send('Insufficient scope');
  } else {
    // Handle other types of errors
    logger.error(`Express error | ${err.name || 'N/A'} | ${err.message || 'N/A'}`, { error: err });
    res.status(500).send('Internal Server Error');
  }
};
