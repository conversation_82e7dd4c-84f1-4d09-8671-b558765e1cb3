import { Response, NextFunction } from 'express';
import logger from '../../common/utils/logger.js';
import { BaseApiRequest } from '../../types.js';

export const logApiCalls = (req: BaseApiRequest, res: Response, next: NextFunction): void => {
  logger.info(req.path, {
    http: {
      statusCode: res.statusCode,
      method: req.method,
      path: req.path,
      query: req.query,
      body: req.body,
    },
    user: {
      id: req.userId,
      email: req.userEmail,
    },
    statusCode: res.statusCode,
  });
  next();
};
