import { Response, NextFunction } from 'express';
import compression from 'compression';
import { BaseApiRequest } from '../../types.js';

export const compressionMiddleware = (
  req: BaseApiRequest,
  res: Response,
  next: NextFunction,
): void => {
  compression({
    threshold: 1024,
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    },
    level: 6,
  })(req, res, next);
};
