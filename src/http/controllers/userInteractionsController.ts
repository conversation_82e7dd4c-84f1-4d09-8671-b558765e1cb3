import { Response } from 'express';
import * as userInteractionsService from '../services/userInteractions.js';
import logger from '../../common/utils/logger.js';
import { AISuggestionAction, UserInteractionsRequest } from '../../types.js';

export const getAISuggestionsSummary = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const { insightId } = req.params;
  const orgId = req.orgId;

  try {
    const aiSuggestionsSummary = await userInteractionsService.getAISuggestionsSummary(
      insightId,
      orgId as string,
    );
    logger.info('Fetched AI Suggestions Summary', { aiSuggestionsSummary });
    res.json({ aiSuggestionsSummary });
  } catch (error) {
    logger.error('Error fetching AI Suggestion counters:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getAISuggestionsLatest = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const { insightId } = req.params;
  const { userId } = req.query;
  const orgId = req.orgId;

  try {
    const aiSuggestions = await userInteractionsService.getAISuggestionsLatest(
      insightId,
      userId as string,
      orgId as string,
    );
    logger.info('Fetched a user`s latest AI Suggestions', { aiSuggestions });
    res.json({ aiSuggestions });
  } catch (error) {
    logger.error('Error fetching AI Suggestion clicks:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const createAISuggestionInteraction = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const {
    params: { insightId },
    body: action,
    userId,
    orgId,
  } = req;

  try {
    const newInteraction = await userInteractionsService.createAISuggestionInteraction(insightId, {
      userId,
      orgId,
      ...(action as AISuggestionAction),
    });
    logger.info('Created a new AI Suggestion interaction', { newInteraction });
    res.status(201).json(newInteraction);
  } catch (error) {
    logger.error('Error creating new AI Suggestion interaction:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const createHypothesisClick = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const {
    params: { insightId },
    body: action,
    userId,
    orgId,
  } = req;

  try {
    const newInteraction = await userInteractionsService.createHypothesisClick(insightId, {
      userId,
      orgId,
      ...action,
    });
    logger.info('Created a new Hypothesis Tested Click interaction', { newInteraction });
    res.status(201).json(newInteraction);
  } catch (error) {
    logger.error('Error creating new Hypothesis Tested Click interaction:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const createHypothesisFeedback = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const {
    params: { insightId },
    body: action,
    userId,
    orgId,
  } = req;

  try {
    const newInteraction = await userInteractionsService.createHypothesisFeedback(insightId, {
      userId,
      orgId,
      ...action,
    });
    logger.info('Created a new Hypothesis Tested Feedback interaction', { newInteraction });
    res.status(201).json(newInteraction);
  } catch (error) {
    logger.error('Error creating new Hypothesis Tested Feedback interaction:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getAISuggestionsLatestAll = async (
  req: UserInteractionsRequest,
  res: Response,
): Promise<void> => {
  const userIds = typeof req.query.userIds === 'string' ? req.query.userIds.split(',') : undefined;
  const insightIds =
    typeof req.query.insightIds === 'string' ? req.query.insightIds.split(',') : undefined;
  const orgIds = typeof req.query.orgIds === 'string' ? req.query.orgIds.split(',') : undefined;
  const skip = req.query.skip ? parseInt(req.query.skip as string, 10) : undefined;
  const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : undefined;
  const startDate = req.query.startDate as string | undefined;
  const endDate = req.query.endDate as string | undefined;

  try {
    const aiSuggestions = await userInteractionsService.getAISuggestionsLatestAll(
      { skip, limit },
      { userIds, insightIds, orgIds, startDate, endDate },
    );
    logger.info('Fetched latest AI Suggestion clicks', { aiSuggestions });
    res.json({ aiSuggestions });
  } catch (error) {
    logger.error('Error fetching AI Suggestion clicks', error);
    res.status(500).json({ error: 'Error fetching AI Suggestion clicks' });
  }
};
