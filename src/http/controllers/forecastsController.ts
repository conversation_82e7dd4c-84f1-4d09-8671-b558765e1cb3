import { Response } from 'express';
import logger from '../../common/utils/logger.js';
import * as forecastsService from '../services/forecasts.js';
import { InsightsRequest } from '../../types.js';

export const createForecast = async (req: InsightsRequest, res: Response): Promise<void> => {
  const forecast = req.body;
  try {
    const createdForecast = await forecastsService.createForecast(forecast);
    logger.info('Created forecast', { createdForecast });
    res.json(createdForecast);
  } catch (error: unknown) {
    const e = error as Error & { cause?: string };
    logger.error('Error creating forecast:', e, forecast);
    switch (e.cause) {
      case 'malformed-request':
        res.status(400).json({ error: 'Bad request', message: e.message });
        break;
      default:
        res.status(500).json({ error: 'Internal server error', message: e.message });
    }
  }
};
