import { Request, Response } from 'express';
import _ from 'lodash';
import crypto from 'crypto';
import logger from '@/common/utils/logger.js';
import { AuthenticatedRequest } from '@/types.js';
import { invoke } from '@/common/services/search_v2/api.js';
import { invoke as invoke_v3 } from '@/common/services/search_v4/api.js';

function validateInput(req: Request, res: Response): boolean {
  const { q: userQuery, page } = req.query;
  const orgId = req.query?.orgId || req.params?.orgId;

  if (!userQuery || !_.isString(userQuery)) {
    res.status(422).json({ message: 'Query is required' });
    return false;
  }

  if (!orgId || !_.isString(orgId)) {
    res.status(422).json({ message: 'Organization ID is required' });
    return false;
  }

  if (Number(page) < 1 || Number(page) > 100) {
    res.status(422).json({ message: 'Invalid page number' });
    return false;
  }

  return true;
}

const search = async (req: Request, res: Response): Promise<Response | false> => {
  const authenticatedRequest = req as AuthenticatedRequest;
  const { q: userQuery, page } = authenticatedRequest.query;
  const orgId = authenticatedRequest.params.orgId;
  const tokenOrgId = authenticatedRequest.orgId;

  const accessToken = authenticatedRequest.headers.authorization as string;
  const sessionId = crypto.createHash('sha256').update(accessToken, 'utf8').digest('hex');
  const userEmail = authenticatedRequest.userEmail;

  if (orgId !== tokenOrgId) {
    return res.status(401).json({ error: 'Unauthorized request' });
  }

  try {
    const pageNum = parseInt(String(page)) || 1;
    const userQueryStr = userQuery as string;

    logger.info('Running search query', { query: { orgId, userQuery, pageNum } });
    if (!validateInput(req, res)) return false;

    const { insights, filteredCount } = await invoke(
      {
        userQuery: userQueryStr,
        orgId,
        page: pageNum,
      },
      {
        sessionId,
        userEmail,
        env: process.env.PL_ENV || 'local',
      },
    );

    return res.json({
      insights,
      totalCount: filteredCount,
      filteredCount,
      currentFilters: {},
    });
  } catch (error) {
    logger.error('Failed to run search', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

const search_v4 = async (req: Request, res: Response): Promise<Response | false> => {
  const authenticatedRequest = req as AuthenticatedRequest;
  const { q: userQuery, page } = authenticatedRequest.query;
  const orgId = authenticatedRequest.params.orgId;
  const tokenOrgId = authenticatedRequest.orgId;

  const accessToken = authenticatedRequest.headers.authorization as string;
  const sessionId = crypto.createHash('sha256').update(accessToken, 'utf8').digest('hex');
  const userEmail = authenticatedRequest.userEmail;

  if (orgId !== tokenOrgId) {
    return res.status(401).json({ error: 'Unauthorized request' });
  }

  try {
    const pageNum = parseInt(String(page)) || 1;
    const userQueryStr = userQuery as string;

    logger.info('Running search query', { query: { orgId, userQuery, pageNum } });
    if (!validateInput(req, res)) return false;

    const { insights, filteredCount } = await invoke_v3(
      {
        userQuery: userQueryStr,
        orgId,
        page: pageNum,
      },
      {
        sessionId,
        userEmail,
        env: process.env.PL_ENV || 'local',
      },
    );

    return res.json({
      insights,
      totalCount: filteredCount,
      filteredCount,
      currentFilters: {},
    });
  } catch (error) {
    logger.error('Failed to run search', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

export { search, search_v4 };
