import { Response } from 'express';
import logger from '../../common/utils/logger.js';
import * as searchService from '../services/search.js';
import * as insightsService from '../services/insights.js';
import { InsightsRequest } from '../../types.js';
import { stringToBoolean } from '../utils/http.js';

const getInsight = async (req: InsightsRequest, res: Response): Promise<void> => {
  const { insightId } = req.params;
  const orgId = req.orgId as string;
  const userOrgs = req.userOrgs ?? [];

  try {
    const insight = await insightsService.getInsight(
      insightId,
      userOrgs.map((o: { id: string }) => o.id),
    );
    if (!insight) {
      logger.warn('Insight not found', { insightId });
      res.status(404).json({ error: 'Insight not found' });
      return;
    }
    if (insight.orgId !== orgId) {
      res.status(409).json({
        message: 'Wrong Organization',
        correctOrg: userOrgs.find((o: { id: string }) => o.id === insight.orgId),
      });
      return;
    }
    logger.info('Retrieved single insight', { insightId });
    res.json(insight);
  } catch (error: unknown) {
    logger.error('Error retrieving insight:', { error, insightId });
    res.status(500).json({ we: 'Internal server error' });
  }
};

const createInsight = async (req: InsightsRequest, res: Response): Promise<void> => {
  const insight = req.body;

  try {
    const createdInsight = await insightsService.createInsight(insight);
    const searchIndexResult = await searchService.indexInsightForSearch(createdInsight);

    logger.info('Created insight', { createdInsight, searchIndexResult });
    res.json(createdInsight);
  } catch (error: unknown) {
    const e = error as Error & { cause?: string };
    logger.error('Error creating insight:', e, insight);
    switch (e.cause) {
      case 'malformed-request':
        res.status(400).json({ error: 'Bad request', message: e.message });
        break;
      default:
        res.status(500).json({ error: 'Internal server error', message: e.message });
    }
  }
};

const updateInsight = async (req: InsightsRequest, res: Response): Promise<void> => {
  const { insightId } = req.params;
  const updateData = req.body;
  const orgId = req.orgId as string;

  try {
    const updatedInsight = await insightsService.updateInsight(insightId, updateData, orgId);

    if (!updatedInsight) {
      logger.warn('Insight not found for update', { insightId });
      res
        .status(404)
        .json({ error: 'Not found', message: `Insight with id ${insightId} not found.` });
      return;
    }

    await searchService.updateSearchIndex(insightId, updateData);

    logger.info('Updated insight', { insightId });
    res.json(updatedInsight);
  } catch (error: unknown) {
    const e = error as Error & { cause?: string };
    logger.error('Error updating insight:', e, insightId);
    switch (e.cause) {
      case 'malformed-request':
        res.status(400).json({ error: 'Bad request', message: e.message });
        break;
      default:
        res.status(500).json({ error: 'Internal server error', message: e.message });
    }
  }
};

const deleteInsight = async (req: InsightsRequest, res: Response): Promise<void> => {
  const { insightId } = req.params;
  const orgId = req.orgId as string;

  try {
    const deletedInsightId = await insightsService.deleteInsight(insightId, orgId);

    if (!deletedInsightId) {
      logger.warn('Insight not found for deletion', { insightId });
      res
        .status(404)
        .json({ error: 'Not found', message: `Insight with id ${insightId} not found.` });
      return;
    }

    const searchRes = await searchService.deleteSearchIndex(insightId);
    if (!searchRes) {
      logger.warn('Search index deletion failed', { insightId });
    } else {
      logger.info('Deleted insight', { insightId: deletedInsightId, searchIndexId: searchRes.id });
    }

    res.sendStatus(204);
  } catch (error: unknown) {
    logger.error('Error deleting insight:', error, insightId);

    const e = error as Error & { cause?: string };

    if (e.cause === 'database-error') {
      res.status(500).json({ error: 'Internal server error', message: 'Failed to delete insight' });
      return;
    }

    res.status(500).json({ error: 'Internal server error', message: e.message });
  }
};

const getInsights = async (req: InsightsRequest, res: Response): Promise<void> => {
  const insightIds =
    typeof req.query.insightIds === 'string' ? req.query.insightIds.split(',') : [];
  const uuids = typeof req.query.uuids === 'string' ? req.query.uuids.split(',') : [];
  const orgIds = typeof req.query.orgIds === 'string' ? req.query.orgIds.split(',') : [];
  const skip = req.query.skip ? Number(req.query.skip) : undefined;
  const limit = req.query.limit ? Number(req.query.limit) : undefined;
  const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
  const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
  const isSummary = stringToBoolean(req.query.isSummary as string);
  const isTotal = stringToBoolean(req.query.isTotal as string);
  const breakdownKey = req.query.breakdownKey as string | undefined;
  const breakdownValue = req.query.breakdownValue as string | undefined;
  const granularity = req.query.granularity as string | undefined;

  try {
    const insights = await insightsService.getInsights(
      { skip, limit },
      {
        insightIds,
        uuids,
        orgIds,
        startDate,
        endDate,
        isSummary,
        isTotal,
        breakdownKey,
        breakdownValue,
        granularity,
      },
    );
    logger.info('Retrieved insights', { count: insights.length });
    res.json({ insights });
  } catch (error: unknown) {
    const e = error as Error & { cause?: string };
    logger.error('Error retrieving insights', { e, query: req.query });
    res.status(500).json({ error: 'Error retrieving insights', message: e.message });
  }
};

export { getInsight, createInsight, updateInsight, deleteInsight, getInsights };
