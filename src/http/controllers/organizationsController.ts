import { Response } from 'express';
import logger from '../../common/utils/logger.js';
import * as insightsService from '../services/insights.js';
import * as userInteractionsService from '../services/userInteractions.js';
import { InsightsRequest } from '../../types.js';
import { stringToBoolean } from '../utils/http.js';
import * as forecastsService from '../services/forecasts.js';

export const getOrganizationInsights = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  logger.info('Retrieving insights for organization', { q: req.query });
  const skip = req.query.skip ? Number(req.query.skip) : undefined;
  const limit = req.query.limit ? Number(req.query.limit) : undefined;
  const uuids =
    typeof req.query.uuids === 'string' ? (req.query.uuids as string).split(',') : undefined;
  const visible = stringToBoolean(req.query.visible as string);
  const sensitivity =
    typeof req.query.sensitivity === 'string' ? req.query.sensitivity.split(',') : undefined;
  const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
  const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
  const isTotal = stringToBoolean(req.query.isTotal as string);
  const isSummary = stringToBoolean(req.query.isSummary as string);
  const granularity = req.query.granularity as string | undefined;
  const breakdownKey = req.query.breakdownKey as string | undefined;
  const breakdownValue = req.query.breakdownValue as string | undefined;
  const orgId = req.params.orgId;
  const tokenOrgId = req.orgId;

  if (orgId !== tokenOrgId) {
    res.status(401).json({ error: 'Unauthorized request' });
    return;
  }

  try {
    const insights = await insightsService.getOrganizationInsights(
      orgId,
      { skip, limit },
      {
        uuids,
        visible,
        sensitivity,
        startDate,
        endDate,
        isTotal,
        isSummary,
        granularity,
        breakdownKey,
        breakdownValue,
      },
    );
    logger.info('Retrieved multiple insights', { insightIds: insights?.map(x => x.id) });
    res.json({ insights });
  } catch (error) {
    logger.error('Error retrieving insights:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getOrganizationInsightsCount = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  const visible = stringToBoolean(req.query.visible as string);
  const sensitivity =
    typeof req.query.sensitivity === 'string' ? req.query.sensitivity.split(',') : undefined;
  const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
  const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
  const isTotal = stringToBoolean(req.query.isTotal as string);
  const isSummary = stringToBoolean(req.query.isSummary as string);
  const granularity = req.query.granularity as string | undefined;
  const breakdownKey = req.query.breakdownKey as string | undefined;
  const breakdownValue = req.query.breakdownValue as string | undefined;
  const orgId = req.params.orgId;
  const tokenOrgId = req.orgId;

  if (orgId !== tokenOrgId) {
    res.status(401).json({ error: 'Unauthorized request' });
    return;
  }

  try {
    const count = await insightsService.getOrganizationInsightsCount(orgId, {
      visible,
      sensitivity,
      startDate,
      endDate,
      isTotal,
      isSummary,
      granularity,
      breakdownKey,
      breakdownValue,
    });
    logger.info('Retrieved insights count', { count });
    res.json({ filteredCount: count });
  } catch (error) {
    logger.error('Error retrieving insights count:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getAISuggestionsLatestByDate = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  const dateFrom = req.query.dateFrom as string | undefined;
  const dateTo = req.query.dateTo as string | undefined;
  const orgId = req.params.orgId;
  const tokenOrgId = req.orgId;

  if (orgId !== tokenOrgId) {
    res.status(401).json({ error: 'Unauthorized request' });
    return;
  }

  try {
    const aiSuggestions = await userInteractionsService.getAISuggestionsLatestByDate(
      { dateFrom, dateTo },
      orgId,
    );
    logger.info('Fetched all latest AI Suggestions', { aiSuggestions });
    res.json({ aiSuggestions });
  } catch (error) {
    logger.error('Error fetching all AI Suggestion clicks:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getOrganizationForecast = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  const { forecastId } = req.params;
  const orgId = req.orgId as string;
  const userOrgs = req.userOrgs ?? [];

  try {
    const forecast = await forecastsService.getOrganizationForecast(
      forecastId,
      userOrgs.map((o: { id: string }) => o.id),
    );
    if (!forecast) {
      logger.warn('Forecast not found', { forecastId });
      res.status(404).json({ error: 'Forecast not found' });
      return;
    }
    if (forecast.orgId !== orgId) {
      res.status(409).json({
        message: 'Wrong Organization',
        correctOrg: userOrgs.find((o: { id: string }) => o.id === forecast.orgId),
      });
      return;
    }
    logger.info('Retrieved single forecast', { forecastId });
    res.json(forecast);
  } catch (error: unknown) {
    logger.error('Error retrieving forecast:', { error, forecastId });
    res.status(500).json({ we: 'Internal server error' });
  }
};

export const getLatestOrganizationForecast = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  const orgId = req.params.orgId;
  const userOrgs = req.userOrgs ?? [];
  const userOrgIds = userOrgs.map((o: { id: string }) => o.id);

  if (!userOrgIds.includes(orgId)) {
    res.status(401).json({ error: 'Unauthorized request' });
    return;
  }

  try {
    const forecast = await forecastsService.getLatestOrganizationForecast(orgId);

    if (forecast) {
      logger.info('Retrieved single forecast', { id: forecast?.id });
      res.json(forecast);
      return;
    } else {
      logger.info('No latest forecast for this org', { orgId });
      res.status(404).json({ error: 'No forecasts found' });
      return;
    }
  } catch (error: unknown) {
    logger.error('Error retrieving latest forecast:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getOrganizationForecasts = async (
  req: InsightsRequest,
  res: Response,
): Promise<void> => {
  const orgId = req.params.orgId;
  const tokenOrgId = req.orgId;

  if (orgId !== tokenOrgId) {
    res.status(401).json({ error: 'Unauthorized request' });
    return;
  }

  try {
    const forecasts = await forecastsService.getOrganizationForecasts(orgId);
    logger.info('Retrieved multiple forecasts', { forecasts: forecasts });
    res.json({ forecasts });
  } catch (error) {
    logger.error('Error retrieving forecasts:', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
};
