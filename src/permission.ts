export const Permission = {
  ReadInsights: 'read:insights',
  UpdateInsights: 'update:insights',
  ReadUserInteractions: 'read:user_interactions',
  UpdateUserInteractions: 'update:user_interactions',
  ReadInsightsAll: 'read:insights:all',
  ReadUserInteractionsAll: 'read:user_interactions:all',
  ReadRevenueForecasts: 'read:revenue_forecasts',
  ReadRevenueForecastsAll: 'read:revenue_forecasts:all',
  UpdateRevenueForecasts: 'update:revenue_forecasts',
} as const;

export type Permission = (typeof Permission)[keyof typeof Permission];
