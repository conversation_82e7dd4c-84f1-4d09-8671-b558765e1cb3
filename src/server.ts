import app from './http/index.js';
import * as pgConn from './common/utils/pgConn.js';
import * as redis from './common/utils/redis.js';
import './common/services/search_v4/init.js';

// see https://github.com/aws-samples/graceful-shutdown-with-aws-lambda
process.on('SIGTERM', pgConn.gracefullyShutdown);
process.on('SIGINT', pgConn.gracefullyShutdown);

void redis.client.connect();

await redis.client.del(
  'agent-call:"textToSql"|"org_U5eGLkpe6H7zXQGN"|"fetch insights from january where the channel hypothesis is retained"',
);

const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
