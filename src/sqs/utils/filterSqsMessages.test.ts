import { filterOutFaultyNotifs } from './filterSqsMessages.js';
import { SQSEvent } from 'aws-lambda';

const commonFields = {
  receiptHandle: 'abc-123',
  attributes: {
    ApproximateReceiveCount: '1',
    SentTimestamp: '1234567890123',
    SenderId: 'AID1234567890',
    ApproximateFirstReceiveTimestamp: '1234567890123',
  },
  messageAttributes: {},
  md5OfBody: 'a12b34c567de89f0g123456789012h34',
};

const testPayload: SQSEvent = {
  Records: [
    {
      messageId: 'aad776fc-2808-450a-9c45-b54c7aa4c723',
      body:
        '{' +
        '  "Type" : "Notification",' +
        '  "MessageId" : "a08fbc96-8ca3-5202-a46e-e20c522c9b68",' +
        '  "TopicArn" : "arn:aws:sns:us-east-1:720919091850:collaboration_ticket_cud_events",' +
        '  "Message" : "{\\"event-type\\":\\"ticket-created\\",\\"message\\":{\\"msg\\":\\"tc\\"}}"' +
        '}',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: 'eb71848b-8386-407e-8423-65455c1a7f1d',
      body:
        '{' +
        '  "Type" : "Notification",' +
        '  "MessageId" : "09171adf-61b5-586c-90df-4445bf254106",' +
        '  "TopicArn" : "arn:aws:sns:us-east-1:720919091850:collaboration_ticket_cud_events",' +
        '  "Message" : "{\\"event-type\\":\\"ticket-updated\\",\\"message\\":{\\"msg\\":\\"tu\\"}}"' +
        '}',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: 'eb71848b-8386-407e-8423-65455c1a7f1d',
      body:
        '{' +
        '  "Type" : "Notification",' +
        '  "MessageId" : "09171adf-61b5-586c-90df-4445bf254106",' +
        '  "TopicArn" : "arn:aws:sns:us-east-1:720919091850:collaboration_ticket_cud_events",' +
        '  "Message" : "{\\"event-type\\":\\"ticket-deleted\\",\\"message\\":{\\"msg\\":\\"td\\"}}"' +
        '}',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: '4fb143ce-b960-41a6-b546-b6c69146bd68',
      body: 'non-json body',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: 'f34720ca-6c54-4bb2-bcfe-a841ca95a26c',
      body: '{"Type":"something-else"}',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: 'f34720ca-6c54-4bb2-bcfe-a841ca95a26c',
      body: JSON.stringify({
        Type: 'Notification',
        MessageId: '09171adf-61b5-586c-90df-4445bf254107',
        TopicArn: 'arn:aws:sns:us-east-1:720919091850:airflow_something',
        Message: '{"stringified":"insight"}',
        MessageAttributes: {
          targetEnvironment: { Type: 'String', Value: 'test' },
          version: { Type: 'String', Value: 'v2.0' },
          flow: { Type: 'String', Value: 'tywin' },
        },
      }),
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
    {
      messageId: 'f34720ca-6c54-4bb2-bcfe-a841ca95a26c',
      body: JSON.stringify({
        Type: 'Notification',
        MessageId: '09171adf-61b5-586c-90df-4445bf254107',
        TopicArn: 'arn:aws:sns:us-east-1:720919091850:airflow_something',
        Message: '{"another":"insight","different":"minor-version"}',
        MessageAttributes: {
          targetEnvironment: { Type: 'String', Value: 'test' },
          version: { Type: 'String', Value: 'v2.1' },
          flow: { Type: 'String', Value: 'tywin' },
        },
      }),
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
      awsRegion: 'us-east-1',
    },
  ].map(record => ({ ...commonFields, ...record })),
};

describe('filterOutFaultyNotifs', () => {
  it('should only keep relevant records', () => {
    const result = filterOutFaultyNotifs(testPayload.Records);

    const expectedResult = [
      {
        stringified: 'insight',
      },
      { another: 'insight', different: 'minor-version' },
    ];

    expect(result).toStrictEqual(expectedResult);
  });
});
