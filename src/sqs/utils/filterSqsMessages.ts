import logger from '../../common/utils/logger.js';
import { SNSMessage, SQSRecord } from 'aws-lambda';
import { ParsedMessage } from '../../types.js';
import { Insight } from '../../types.js';

function parsedJsonOrNull(record: SQSRecord): SNSMessage | null {
  try {
    return JSON.parse(record.body);
  } catch {
    logger.warn('Ignoring non-JSON SQS message', { record });
    return null;
  }
}

function jsonSnsNotifOrNull(body: SNSMessage | null): ParsedMessage | null {
  if (!body || body?.Type !== 'Notification') {
    logger.warn('Ignoring non-SNS message', { body });
    return null;
  }
  try {
    return {
      MessageAttributes: body?.MessageAttributes,
      Message: JSON.parse(body?.Message),
    };
  } catch {
    logger.warn('Ignoring non-JSON SNS message', { body });
    return null;
  }
}

function airflowNotifOrNull(message: ParsedMessage | null): Insight | null {
  if (
    message?.MessageAttributes?.flow?.Value !== 'tywin' ||
    !message?.MessageAttributes?.version?.Value?.startsWith('v2')
  ) {
    logger.warn('Ignoring non-Airflow message', { message });
    return null;
  }
  return message.Message as Insight;
}

const filterOutFaultyNotifs = (records: SQSRecord[]): Insight[] =>
  records
    .map(parsedJsonOrNull)
    .map(jsonSnsNotifOrNull)
    .map(airflowNotifOrNull)
    .filter((x): x is Insight => x !== null);

export { filterOutFaultyNotifs };
