import { filterOutFaultyNotifs } from './utils/filterSqsMessages.js';
import { indexInsightsForSearch } from '../common/services/searchService.js';
import { validateAndStoreInsightsTrx } from '../common/services/insightsService.js';
import { kysely } from '../common/utils/pgConn.js';
import logger from '../common/utils/logger.js';
import { RevenueTicket } from '../types.js';
import { Insight } from '../types.js';
import { SQSEvent } from 'aws-lambda';

const validateAndStoreInsights = async (insights: Insight[]): Promise<Insight[]> => {
  const result = await kysely.transaction().execute(async trx => {
    const createdInsights = await validateAndStoreInsightsTrx(insights, trx);
    return createdInsights;
  });
  logger.info(`All insights successfully inserted into the database.`);
  return result;
};

async function processSqsEvent(
  event: SQSEvent,
  _context?: unknown,
): Promise<[Insight[], RevenueTicket]> {
  const insights = filterOutFaultyNotifs(event.Records);
  const createdInsights = await validateAndStoreInsights(insights);
  const createdSearchIndex = (await indexInsightsForSearch(createdInsights)) as RevenueTicket;
  return [createdInsights, createdSearchIndex];
}

export { processSqsEvent, validateAndStoreInsights };
