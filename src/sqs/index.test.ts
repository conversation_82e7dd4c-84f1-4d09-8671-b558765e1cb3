import '../../test/tools/usePostgres';
import '../../test/tools/useRedis';
import { kysely } from '../common/utils/pgConn.js';
import testInsight from '../../test/resources/airflow/testTicket.json' with { type: 'json' };
import logger from '../common/utils/logger.js';
import _ from 'lodash';
import { SQSEvent } from 'aws-lambda';

// Mock OpenAI service
const openAIServiceMock = {
  generateEmbedding: jest.fn(() => ({
    data: [{ embedding: [1, 2, 3] }],
    usage: { total_tokens: 5 },
  })),
};

// Use unstable_mockModule to register the mock before the module under test is imported.
// @ts-expect-error - see https://jestjs.io/docs/ecmascript-modules#module-mocking-in-esm
jest.unstable_mockModule('@/common/services/openAIService.js', () => openAIServiceMock);

let processSqsEvent: (event: SQSEvent) => Promise<unknown>;

beforeAll(async () => {
  // Dynamically import the module AFTER the mock is set up.
  const module = await import('./index.js');
  processSqsEvent = module.processSqsEvent;
});

describe('processSqsEvent', () => {
  it('should process an SQS event and create records in all relevant tables', async () => {
    // Create SQS event with test insight
    const sqsEvent: SQSEvent = {
      Records: [
        {
          messageId: '123',
          eventSource: 'aws:sqs',
          eventSourceARN: 'arn:aws:sqs:us-east-1:720919091850:insights_app_be',
          awsRegion: 'us-east-1',
          body: JSON.stringify({
            Type: 'Notification',
            MessageAttributes: {
              flow: { Type: 'String', Value: 'tywin' },
              version: { Type: 'String', Value: 'v2.21' },
            },
            Message: JSON.stringify(testInsight),
          }),
          receiptHandle: 'abc-123',
          attributes: {
            ApproximateReceiveCount: '1',
            SentTimestamp: '1234567890123',
            SenderId: 'AID1234567890',
            ApproximateFirstReceiveTimestamp: '1234567890123',
          },
          messageAttributes: {},
          md5OfBody: 'a12b34c567de89f0g123456789012h34',
        },
      ],
    };

    // Process the event
    await processSqsEvent(sqsEvent);

    // Verify insight record was created
    const insightRecord = await kysely
      .selectFrom('insights')
      .selectAll()
      .where('id', '=', testInsight.id)
      .executeTakeFirst();

    if (!insightRecord) {
      throw new Error('Insight record not found');
    }

    const testInsightComparison = _.omit(testInsight, 'incidentTimeStamp');
    const insightRecordComparison = _.omit(insightRecord, [
      'createdAt',
      'updatedAt',
      'isTotal',
      'incidentTimeStamp',
    ]);

    expect(_.isEqual(insightRecordComparison, testInsightComparison)).toBe(true);

    // Verify revenue ticket record was created
    const searchRecord = await kysely
      .selectFrom('revenue_ticket')
      .selectAll()
      .where('insight_id', '=', insightRecord.displayId)
      .executeTakeFirst();

    expect(searchRecord).toBeTruthy();
    expect(searchRecord).toEqual({
      id: 3,
      orgId: 'org_kn4GmPzjJityn',
      currency: '$',
      revenueExpectedDeviationPct: 1.8385265,
      revenueDelta: 675.7178,
      productViewsRevenueImpact: -8,
      cartsRevenueImpact: -11,
      checkoutsRevenueImpact: 419,
      ordersRevenueImpact: 236,
      aovRevenueImpact: 41,
      revenueActual: 1043.25,
      incidentTimeStamp: new Date('2024-10-08T00:00:00.000Z'),
      sensitivity: 'medium',
      issueStatus: 'unresolved',
      rawGrapeJson: {},
      visible: true,
      insightId: 123,
      granularity: 'daily',
      isSummary: false,
    });

    if (!searchRecord) {
      throw new Error('Revenue ticket record not found');
    }

    // Verify embedding record was created
    const embeddingRecords = await kysely
      .selectFrom('revenue_ticket_embedding')
      .selectAll()
      .where('revenue_ticket_id', '=', searchRecord.id)
      .execute();

    logger.warn('embeddingRecords', embeddingRecords);

    expect(
      embeddingRecords.map(({ revenueTicketField, textValue, embedding }) => ({
        revenueTicketField,
        textValue,
        embedding,
      })),
    ).toStrictEqual([
      { revenueTicketField: 'category', textValue: testInsight.title, embedding: '[1,2,3]' },
      {
        revenueTicketField: 'executiveSummary',
        textValue: testInsight.executiveSummary.join(' '),
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'overview',
        textValue: testInsight.overview.join(' '),
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'keyInsights',
        textValue: testInsight.keyInsights.join(' '),
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'visualSummary',
        textValue: testInsight.visualSummary,
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'incidentDetails',
        textValue: testInsight.incidentDetails,
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'rootCauseAnalysisSummary',
        textValue: testInsight.rootCauseAnalysisSummary,
        embedding: '[1,2,3]',
      },
      {
        revenueTicketField: 'rootCauseAnalysisDetails',
        textValue: testInsight.rootCauseAnalysisDetails,
        embedding: '[1,2,3]',
      },
    ]);

    // Cleanup
    await kysely.transaction().execute(async trx => {
      await trx
        .deleteFrom('revenue_ticket_embedding')
        .where('revenue_ticket_id', '=', searchRecord.id)
        .execute();
      await trx.deleteFrom('revenue_ticket').where('insightId', '=', searchRecord.id).execute();
      await trx.deleteFrom('insights').where('id', '=', testInsight.id).execute();
    });
  });
});
