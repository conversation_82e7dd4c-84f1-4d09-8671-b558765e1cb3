import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { RedisContainer } from '@testcontainers/redis';

async function setupPostgres() {
  const pgContainer = await new PostgreSqlContainer('pgvector/pgvector:pg14').start();
  globalThis.__POSTGRES__ = pgContainer;

  process.env.DB_HOST = pgContainer.getHost();
  process.env.DB_NAME = pgContainer.getDatabase();
  process.env.DB_PASSWORD = pgContainer.getPassword();
  process.env.DB_PORT = pgContainer.getPort().toString();
  process.env.DB_USERNAME = pgContainer.getUsername();

  const { pgPool } = await import('./dist/src/common/utils/pgConn.js');
  await pgPool.query('CREATE EXTENSION IF NOT EXISTS vector;');
  await pgPool.end();
}

async function setupRedis() {
  const redisContainer = await new RedisContainer().start();
  globalThis.__REDIS__ = redisContainer;
  process.env.REDIS_URL = redisContainer.getConnectionUrl();
}

export default async () => {
  const { default: logger } = await import('./dist/src/common/utils/logger.js');
  
  logger.info('Global setup');
  await setupRedis();
  await setupPostgres();
  logger.info('Global setup done');
};