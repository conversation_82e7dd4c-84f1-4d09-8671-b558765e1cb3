/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.addColumns('insights', {
    is_total: { type: 'boolean', notNull: true, default: false },
    is_summary: { type: 'boolean', notNull: true, default: false },
    breakdown: { type: 'jsonb', notNull: true, default: '[]' },
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropColumns('insights', ['is_total', 'is_summary', 'breakdown']);
};
