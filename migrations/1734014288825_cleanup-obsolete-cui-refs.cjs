/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.dropColumn('insights', 'cui_id');
  pgm.dropColumn('revenue_ticket', 'cui_id');
  pgm.alterColumn('revenue_ticket', 'insight_id', {
    notNull: true,
  });
  pgm.addConstraint('revenue_ticket', 'revenue_ticket_insight_id_key', {
    unique: 'insight_id',
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = _pgm => {
  // no down migration because we'd need to populate revenue_ticket.cui_id with data
};
