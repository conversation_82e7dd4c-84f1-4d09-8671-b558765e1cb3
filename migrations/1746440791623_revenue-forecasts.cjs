/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.createTable('revenue_forecast_reports', {
    id: {
      type: 'integer',
      primaryKey: true,
      sequenceGenerated: {
        precedence: 'BY DEFAULT',
      },
    },
    summary: { type: 'TEXT' },
    period_start: { type: 'DATE' },
    forecast_window: {
      type: 'VARCHAR(32)',
      check: "forecast_window IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')",
    },
    run_date: { type: 'TIMESTAMP' },
    next_run_date: { type: 'TIMESTAMP' },
    org_id: { type: 'VARCHAR(128)' },
    execution_id: { type: 'VARCHAR(512)' },
    key_metric: { type: 'JSONB' },
    revenue_groups: { type: 'JSONB' },
  });

  pgm.createIndex('revenue_forecast_reports', ['org_id', { name: 'period_start', sort: 'desc' }]);
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropIndex('revenue_forecast_reports', ['org_id', { name: 'period_start', sort: 'desc' }]);
  pgm.dropTable('revenue_forecast_reports');
};
