/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.alterColumn('revenue_ticket', 'product_views_revenue_impact', { notNull: false });
  pgm.alterColumn('revenue_ticket', 'carts_revenue_impact', { notNull: false });
  pgm.alterColumn('revenue_ticket', 'checkouts_revenue_impact', { notNull: false });
  pgm.alterColumn('revenue_ticket', 'orders_revenue_impact', { notNull: false });
  pgm.alterColumn('revenue_ticket', 'aov_revenue_impact', { notNull: false });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.alterColumn('revenue_ticket', 'product_views_revenue_impact', { notNull: true });
  pgm.alterColumn('revenue_ticket', 'carts_revenue_impact', { notNull: true });
  pgm.alterColumn('revenue_ticket', 'checkouts_revenue_impact', { notNull: true });
  pgm.alterColumn('revenue_ticket', 'orders_revenue_impact', { notNull: true });
  pgm.alterColumn('revenue_ticket', 'aov_revenue_impact', { notNull: true });
};
