/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.addColumn('insights', {
    breakdown_dimension: {
      type: 'jsonb',
      notNull: true,
      default: '{}',
    },
  });

  pgm.sql(`
    UPDATE insights
    SET breakdown_dimension = (breakdown->0)::jsonb
  `);

  pgm.dropColumn('insights', 'breakdown');
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.addColumn('insights', {
    breakdown: {
      type: 'jsonb',
      notNull: true,
      default: '[]',
    },
  });

  pgm.sql(`
    UPDATE insights
    SET breakdown = json_build_array(breakdown_dimension)
  `);

  pgm.dropColumn('insights', 'breakdown_dimension');
};
