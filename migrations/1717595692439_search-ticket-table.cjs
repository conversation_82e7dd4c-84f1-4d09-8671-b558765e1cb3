/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.createTable('revenue_ticket', {
    id: { type: 'serial', primaryKey: true },
    cui_id: { type: 'varchar', notNull: true, unique: true },
    org_id: { type: 'varchar(32)', notNull: true },
    currency: { type: 'varchar(16)', notNull: true },
    revenue_expected_deviation_pct: { type: 'float', notNull: true },
    revenue_delta: { type: 'float', notNull: true },
    product_views_revenue_impact: { type: 'integer', notNull: true },
    carts_revenue_impact: { type: 'integer', notNull: true },
    checkouts_revenue_impact: { type: 'integer', notNull: true },
    orders_revenue_impact: { type: 'integer', notNull: true },
    aov_revenue_impact: { type: 'integer', notNull: true },
    revenue_actual: { type: 'float', notNull: true },
    incident_time_stamp: { type: 'timestamp', notNull: true },
    sensitivity: { type: 'varchar(16)', check: "sensitivity IN ('low', 'medium', 'high')" },
    issue_status: {
      type: 'varchar(16)',
      check: "issue_status IN ('unresolved', 'resolved', 'in_progress')",
    },
    raw_grape_json: { type: 'json', notNull: true },
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropTable('revenue_ticket');
};
