/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.createTable('revenue_ticket_embedding', {
    id: { type: 'serial', primaryKey: true },
    revenue_ticket_id: { type: 'integer', notNull: true },
    revenue_ticket_field: { type: 'varchar', notNull: true },
    text_value: { type: 'varchar', notNull: true },
    embedding: { type: 'vector' },
    platform: { type: 'varchar', notNull: true },
    model: { type: 'varchar', notNull: true },
    cost: { type: 'float', notNull: true },
    cost_unit: { type: 'varchar', notNull: true },
    model_params: { type: 'json' },
  });

  pgm.addConstraint('revenue_ticket_embedding', 'fk_revenue_ticket_id', {
    foreignKeys: {
      columns: 'revenue_ticket_id',
      references: 'revenue_ticket(id)',
      onDelete: 'CASCADE',
    },
  });

  pgm.addConstraint('revenue_ticket_embedding', 'uq_revenue_ticket_field_platform_model', {
    unique: ['revenue_ticket_id', 'revenue_ticket_field', 'platform', 'model'],
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropTable('revenue_ticket_embedding');
};
