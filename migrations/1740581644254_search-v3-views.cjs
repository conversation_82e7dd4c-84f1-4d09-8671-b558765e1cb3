exports.shorthands = undefined;

exports.up = pgm => {
  pgm.createView(
    'insights_search',
    {},
    `
      SELECT
        display_id AS insight_id,
        org_id,
        incident_time_stamp,
        title,
        granularity,
        revenue_expected_deviation_pct,
        baseline,
        revenue_delta,
        anomaly_detection_mode,
        sensitivity,
        is_total,
        is_summary,
        breakdown_dimension->>'key' AS breakdown_key,
        breakdown_dimension->>'value' AS breakdown_value,
        key_metric->>'currency' AS currency,
        (key_metric->>'value')::numeric AS value,
        (key_metric->>'last_value')::numeric AS last_value,
        (key_metric->>'last_year_value')::numeric AS last_year_value,
        (key_metric->'expected_range'->>'min')::numeric AS expected_range_min,
        (key_metric->'expected_range'->>'max')::numeric AS expected_range_max,
        row_to_json(insights.*) AS insight_json
      FROM insights
      WHERE visible = true;
    `,
  );

  pgm.createView(
    'insights_search_hypotheses',
    {},
    `
      SELECT
        i.display_id     AS insight_id,
        h->>'id'         AS hypothesis_id,
        h->>'statement'  AS statement,
        h->>'verdict'    AS verdict,
        h->>'state'      AS state,
        h->>'inspector'  AS inspector,
        (
          SELECT array_agg(contributor->>'name')
          FROM json_array_elements(
            COALESCE(h->'result_structured'->'top_contributors', '[]'::json)
          ) AS contributor
        ) AS top_contributors
      FROM insights AS i
      CROSS JOIN LATERAL json_array_elements(i.hypotheses) AS h;
    `,
  );

  pgm.createView(
    'insights_search_funnel',
    {},
    `
      SELECT
        i.display_id                     AS insight_id,
        m->>'id'                         AS metric_id,
        m->>'name'                       AS name,
        (m->>'baselineValue')::float8    AS baseline_value,
        (m->>'actualValue')::float8      AS actual_value,
        (m->>'keyMetricImpact')::float8  AS impact
      FROM insights AS i
      CROSS JOIN LATERAL json_array_elements(i.funnel_metrics) AS m;
    `,
  );
};

exports.down = pgm => {
  pgm.dropView('insights_search_funnel');
  pgm.dropView('insights_search_hypotheses');
  pgm.dropView('insights_search');
};
