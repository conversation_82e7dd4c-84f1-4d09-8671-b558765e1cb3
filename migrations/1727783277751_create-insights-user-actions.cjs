/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.createTable('user_interactions', {
    id: 'id',
    timestamp: {
      type: 'timestamp',
      notNull: true,
      default: pgm.func('current_timestamp'),
    },
    user_id: { type: 'varchar(128)', notNull: true },
    org_id: { type: 'varchar(128)', notNull: true },
    insight_id: { type: 'varchar(128)', notNull: true },
    action_type: { type: 'varchar(128)', notNull: true },
    action_target_id: { type: 'varchar(128)' },
    action_target_group: { type: 'varchar(128)' },
    action_value: { type: 'varchar(128)' },
  });

  pgm.createIndex('user_interactions', 'user_id');
  pgm.createIndex('user_interactions', 'timestamp');
  pgm.createIndex('user_interactions', ['insight_id', 'action_type']);

  // Create view
  pgm.sql(`
    CREATE VIEW user_interactions_latest AS
    SELECT DISTINCT ON (
        user_id,
        insight_id,
        action_type,
        action_target_id,
        action_target_group
      )
      user_id,
      org_id,
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      action_value,
      timestamp
    FROM user_interactions
    ORDER BY
      user_id,
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      timestamp DESC;
  `);

  pgm.sql(`
    CREATE VIEW user_interactions_latest_summary AS
    SELECT
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      action_value,
      COUNT(*) AS interaction_count
    FROM user_interactions_latest
    GROUP BY
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      action_value;
  `);
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropView('user_interactions_latest_summary');
  pgm.dropView('user_interactions_latest');
  pgm.dropTable('user_interactions');
};
