/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.sql(`
    CREATE OR REPLACE VIEW user_interactions_latest_summary AS
    SELECT
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      action_value,
      COUNT(*) AS interaction_count,
      org_id
    FROM user_interactions_latest
    GROUP BY
      org_id,
      insight_id,
      action_type,
      action_target_id,
      action_target_group,
      action_value;
  `);
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = _pgm => {};
// see https://stackoverflow.com/a/65118443
