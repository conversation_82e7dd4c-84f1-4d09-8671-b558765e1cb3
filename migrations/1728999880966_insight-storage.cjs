/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.createTable('insights', {
    id: { type: 'uuid', primaryKey: true },
    display_id: { type: 'serial', notNull: true, unique: true },
    cui_id: { type: 'integer', unique: true },
    org_id: { type: 'varchar(128)', notNull: true },
    visible: { type: 'boolean', notNull: true, default: true },
    incident_time_stamp: { type: 'timestamp', notNull: true },
    title: { type: 'text', notNull: true },
    execution_id: { type: 'varchar(512)', notNull: true },
    ais_execution_id: { type: 'varchar(512)', notNull: true },
    granularity: { type: 'varchar(128)', notNull: true },
    funnel_metrics: { type: 'json', notNull: true },
    hypotheses: { type: 'json', notNull: true },
    executive_summary: { type: 'text[]', notNull: true },
    overview: { type: 'text[]', notNull: true },
    key_insights: { type: 'text[]', notNull: true },
    actions: { type: 'text[]', notNull: true },
    visual_summary: { type: 'text', notNull: true },
    incident_details: { type: 'text', notNull: true },
    root_cause_analysis_summary: { type: 'text', notNull: true },
    root_cause_analysis_details: { type: 'text', notNull: true },
    ai_suggestions: { type: 'text', notNull: true },
    ai_actions: { type: 'json', notNull: true },
    revenue_expected_deviation_pct: { type: 'float', notNull: true },
    baseline: { type: 'float', notNull: true },
    revenue_delta: { type: 'float', notNull: true },
    anomaly_detection_mode: { type: 'varchar(128)', notNull: true },
    sensitivity: { type: 'varchar(128)', notNull: true },
    key_metric: { type: 'json', notNull: true },
    created_at: { type: 'timestamp', notNull: true, default: pgm.func('current_timestamp') },
    updated_at: { type: 'timestamp', notNull: true, default: pgm.func('current_timestamp') },
  });

  pgm.createIndex('insights', 'display_id');
  pgm.createIndex('insights', ['org_id', 'sensitivity', 'visible', 'incident_time_stamp']);
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropTable('insights');
};
