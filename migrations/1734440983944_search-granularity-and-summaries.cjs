/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
exports.shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.up = pgm => {
  pgm.addColumns('revenue_ticket', {
    granularity: {
      type: 'varchar(32)',
      notNull: true,
      default: 'daily',
      check: "granularity IN ('daily', 'weekly', 'monthly', 'yearly')",
    },
    is_summary: {
      type: 'boolean',
      notNull: true,
      default: false,
    },
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
exports.down = pgm => {
  pgm.dropColumns('revenue_ticket', ['granularity', 'is_summary']);
};
