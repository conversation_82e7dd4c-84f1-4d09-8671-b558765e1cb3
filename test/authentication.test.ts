import './tools/usePostgres';
import request from 'supertest';
import listEndpoints from 'express-list-endpoints';
import { App } from 'supertest/types.js';

let app: App;

describe('API Authentication Security', () => {
  beforeAll(async () => {
    app = (await import('../src/http/index.js')).default;
  }, 10000);

  const publicEndpoints = [
    '/status',
    '/api/v1/docs',
    // Add any other public endpoints here
  ];

  it('should verify all endpoints require authentication except public ones', async () => {
    // Get all registered routes with their methods
    const routes = listEndpoints(app).flatMap(route =>
      route.methods.map(method => ({
        path: route.path,
        method: method,
      })),
    );

    // Add Swagger UI endpoint manually
    routes.push({ path: '/api/v1/docs', method: 'GET' });

    console.log('Discovered routes:', routes);

    // Create a test for each route+method combination
    const results = await Promise.all(
      routes.map(async ({ path, method }) => {
        // Skip testing OPTIONS
        if (method === 'OPTIONS') return null;

        // Use supertest to make a request with the appropriate method
        let response;
        const agent = request(app);

        switch (method) {
          case 'GET':
            response = await agent.get(path);
            break;
          case 'POST':
            response = await agent.post(path);
            break;
          case 'PUT':
            response = await agent.put(path);
            break;
          case 'DELETE':
            response = await agent.delete(path);
            break;
          case 'PATCH':
            response = await agent.patch(path);
            break;
          default:
            response = await agent.get(path);
        }

        // Check if this is a public endpoint
        const isPublicEndpoint = publicEndpoints.some(
          publicRoute => path === publicRoute || path.startsWith(`${publicRoute}/`),
        );

        return {
          path,
          method,
          status: response.status,
          isPublic: isPublicEndpoint,
          authorized: !(response.status === 401 || response.status === 403),
        };
      }),
    );

    // Filter out null results (OPTIONS)
    const validResults = results.filter(result => result !== null);

    // Find unauthorized routes (those that don't require auth but should)
    const unauthorizedRoutes = validResults.filter(result => !result.isPublic && result.authorized);

    // Assert all non-public routes require authentication
    expect(unauthorizedRoutes).toEqual([]);

    if (unauthorizedRoutes.length > 0) {
      console.error(`The following routes do not properly require authentication:
        ${JSON.stringify(unauthorizedRoutes, null, 2)}`);
    }

    // Verify public endpoints are accessible
    const inaccessiblePublicRoutes = validResults.filter(
      result => result.isPublic && (result.status === 401 || result.status === 403),
    );

    expect(inaccessiblePublicRoutes).toEqual([]);

    if (inaccessiblePublicRoutes.length > 0) {
      console.error(`The following public routes incorrectly require authentication:
        ${JSON.stringify(inaccessiblePublicRoutes, null, 2)}`);
    }
  });
});
