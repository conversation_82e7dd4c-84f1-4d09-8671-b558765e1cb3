import './tools/usePostgres';
import request from 'supertest';
import { App } from 'supertest/types.js';

let app: App;
describe('GET /', () => {
  beforeAll(async () => {
    app = (await import('../src/http/index.js')).default;
  });
  it('should return invalid token response', async () => {
    return request(app)
      .get('/')
      .expect('Content-Type', /html/)
      .expect(401)
      .then(res => {
        expect(res.statusCode).toBe(401);
      });
  });
});
