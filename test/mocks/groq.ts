import { jest } from '@jest/globals';

export type FakeGroqResponse = {
  toJSON(): { kwargs: { content: string } };
};
export type InvokeFn = (...args: unknown[]) => Promise<FakeGroqResponse>;

// Re‑usable mock that every test can control
export const mockInvoke = jest.fn<InvokeFn>();

// Register the replacement for @langchain/groq once.
// Must be executed **before** any code that imports that package.
jest.unstable_mockModule('@langchain/groq', () => ({
  ChatGroq: jest.fn().mockImplementation(() => ({
    bind: () => ({ invoke: mockInvoke }),
  })),
}));
