import { jest } from '@jest/globals';

// Mock Redis client that can be used across tests
export const mockRedisClient = {
  isOpen: false,
  connect: jest.fn(() => Promise.resolve()),
  disconnect: jest.fn(() => Promise.resolve()),
  quit: jest.fn(() => Promise.resolve()),
  flushAll: jest.fn(() => Promise.resolve()),
  get: jest.fn(() => Promise.resolve(null)),
  set: jest.fn(() => Promise.resolve('OK')),
} as any;

// Register the replacement for @/common/utils/redis.js once.
// Must be executed **before** any code that imports that package.
jest.unstable_mockModule('@/common/utils/redis.js', () => ({
  client: mockRedisClient,
  withCache: jest.fn((_cacheKey, fn) => (fn as any)()), // Simply call the function without caching
  get: jest.fn(() => Promise.resolve(null)),
  set: jest.fn(() => Promise.resolve('OK')),
}));
