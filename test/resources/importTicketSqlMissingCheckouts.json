{"text": "with \"inserted_ticket\" as (insert into \"revenue_ticket\" (\"insight_id\", \"org_id\", \"visible\", \"granularity\", \"is_summary\", \"currency\", \"revenue_expected_deviation_pct\", \"revenue_delta\", \"product_views_revenue_impact\", \"carts_revenue_impact\", \"orders_revenue_impact\", \"aov_revenue_impact\", \"revenue_actual\", \"incident_time_stamp\", \"sensitivity\", \"issue_status\", \"raw_grape_json\") values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17) returning \"id\") insert into \"revenue_ticket_embedding\" (\"revenue_ticket_id\", \"revenue_ticket_field\", \"text_value\", \"embedding\", \"platform\", \"model\", \"cost\", \"cost_unit\", \"model_params\") values ((select \"id\" from \"inserted_ticket\"), $18, $19, $20, $21, $22, $23, $24, $25), ((select \"id\" from \"inserted_ticket\"), $26, $27, $28, $29, $30, $31, $32, $33), ((select \"id\" from \"inserted_ticket\"), $34, $35, $36, $37, $38, $39, $40, $41), ((select \"id\" from \"inserted_ticket\"), $42, $43, $44, $45, $46, $47, $48, $49), ((select \"id\" from \"inserted_ticket\"), $50, $51, $52, $53, $54, $55, $56, $57), ((select \"id\" from \"inserted_ticket\"), $58, $59, $60, $61, $62, $63, $64, $65), ((select \"id\" from \"inserted_ticket\"), $66, $67, $68, $69, $70, $71, $72, $73), ((select \"id\" from \"inserted_ticket\"), $74, $75, $76, $77, $78, $79, $80, $81)", "values": [123, "org_kn4GmPzjJityn", true, "daily", false, "$", 1.8385265, 675.7178, -8, -11, 236, 41, 1043.25, "2024-10-08T00:00:00.000Z", "medium", "unresolved", "{}", "category", "Bottoms_Skirt", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Checkouts increase primarily impacted Revenue The anomaly aligns with interest from P.Max and organic campaigns, \"Lydia Skort\" and similar products, returning users, and competitor activity Focus on enhancing returning user engagement and evaluating key campaigns.", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "overview", "Checkouts increase primarily impacted Revenue Attributed to \"P.Max\" and \"(organic)\" campaigns", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "keyInsights", "Checkouts added $419, boosting revenue by $676 Competitor activity spurred interest in similar products", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "visualSummary", "Checkouts is the top contributing funnel step", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "incidentDetails", "The revenue for the \"Bottoms_Skirt\" product category reached $1,043, a deviation of 184% from the baseline of $368.", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "rootCauseAnalysisSummary", "The top contributor to the revenue change was 'checkouts', which added $419. The 'orders' step contributed $236, and 'Average Order Value (AOV)' added $41. Conversely, 'product_views' and 'carts' negatively impacted revenue with $-8 and $-11, respectively.", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}", "rootCauseAnalysisDetails", "The revenue anomaly was influenced by substantial contributions from \"P.Max | Non Brand | BHFN\" and \"(organic)\" campaigns, responsible for 49% and 32% of the change. Products such as \"Lydia Skort\" and \"Overall Winner Jumper\" had significant contributions. Returning users were a major driver, impacting 87% of the revenue shift. Competitor news regarding Anthropologie's marketing attempts generated interest in similar products.", "[0.1,0.2,0.3]", "openai", "text-embedding-3-large", 5, "token", "{\"dimensions\":1024}"]}