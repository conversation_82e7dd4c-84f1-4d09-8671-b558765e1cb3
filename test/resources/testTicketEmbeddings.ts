import { RevenueTicket } from '../../src/types.js';

export const revenueTickets: RevenueTicket[] = [
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'category',
    text_value: 'shoes',
    embedding_vector: [0, 0, 0],
    platform: 'platform2',
    model: 'model2',
    cost: 20.5,
    cost_unit: 'tokens',
    model_params: { param2: 'value2' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv2Overview',
    text_value:
      'Minor revenue decrease with checkout anomaly. Top product drove carts, channels underperformed.',
    embedding_vector: [1, 1, 1],
    platform: 'platform3',
    model: 'model3',
    cost: 30.5,
    cost_unit: 'tokens',
    model_params: { param3: 'value3' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv1ExecutiveSummary',
    text_value: 'Revenue slipped marginally overnight. Declines in carts, email campaign issues.',
    embedding_vector: [2, 2, 2],
    platform: 'platform4',
    model: 'model4',
    cost: 40.5,
    cost_unit: 'tokens',
    model_params: { param4: 'value4' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv2KeyInsights',
    text_value: 'Carts: -$24 vs. AOV: +$17. Email, Organic channels affecting Revenue.',
    embedding_vector: [3, 3, 3],
    platform: 'platform5',
    model: 'model5',
    cost: 50.5,
    cost_unit: 'tokens',
    model_params: { param5: 'value5' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv2VisualSummary',
    text_value: 'Carts change leading Revenue drop.',
    embedding_vector: [4, 4, 4],
    platform: 'platform6',
    model: 'model6',
    cost: 60.5,
    cost_unit: 'tokens',
    model_params: { param6: 'value6' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv3IncidentDetails',
    text_value:
      "A minor revenue deviation of -28% was noted in the 'Outlet Bottoms' category, incurring a negligible DoD change of $0. Despite this, the revenue fell below the minimum expected amount, leading to a deeper analysis.",
    embedding_vector: [5, 5, 5],
    platform: 'platform7',
    model: 'model7',
    cost: 70.5,
    cost_unit: 'tokens',
    model_params: { param7: 'value7' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv3RootCauseAnalysisSummary',
    text_value:
      'Carts and checkouts downturn drove the anomaly. Orders and AOV increased, raising concerns about the checkout process.',
    embedding_vector: [6, 6, 6],
    platform: 'platform8',
    model: 'model8',
    cost: 80.5,
    cost_unit: 'tokens',
    model_params: { param8: 'value8' },
  },
  {
    revenue_ticket_id: '123',
    revenue_ticket_field: 'lv3RootCauseAnalysisDetails',
    text_value:
      "Within the 'Outlet Bottoms' category, 'Bookstore's Best A-Line Skirt - BLUE / XS' significantly influenced carts. Email's 'This Or That' and Organic Shopping's 'Sweetheart Shop Rewards' campaigns contributed negatively. Returning customers' carts metric fell vastly.",
    embedding_vector: [7, 7, 7],
    platform: 'platform9',
    model: 'model9',
    cost: 90.5,
    cost_unit: 'tokens',
    model_params: { param9: 'value9' },
  },
];
