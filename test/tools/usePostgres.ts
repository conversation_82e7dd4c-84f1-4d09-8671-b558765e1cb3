import { pgPool, gracefullyShutdown } from '../../src/common/utils/pgConn.js';

beforeAll(async () => {
  const migrateModule = await import('node-pg-migrate');
  const migrate = migrateModule.default as unknown as (options: unknown) => Promise<unknown>;

  await migrate({
    dbClient: pgPool,
    dir: 'migrations',
    direction: 'up',
    migrationsTable: 'migrations',
    log: () => {},
  });
  const seed = (await import('../../scripts/seeds/seed.mjs')).default;
  await seed(pgPool);
});

afterAll(async () => {
  const migrateModule = await import('node-pg-migrate');
  const migrate = migrateModule.default as unknown as (options: unknown) => Promise<unknown>;

  await migrate({
    dbClient: pgPool,
    dir: 'migrations',
    direction: 'down',
    migrationsTable: 'migrations',
    log: () => {},
    count: Infinity,
  });
  await gracefullyShutdown();
});
