import './tools/usePostgres';
import insight from './resources/airflow/testTicket.json' with { type: 'json' };
import { SchemaNode } from '../src/types.js';
import SCHEMA from '../contracts/airflow_insight_v1.json' with { type: 'json' };
import { pgPool, kysely } from '../src/common/utils/pgConn.js';
import { sql } from 'kysely';
import { Ajv } from 'ajv';
import _addFormats from 'ajv-formats'; // Workaround - see https://github.com/ajv-validator/ajv-formats/issues/85#issuecomment-2262652443
const addFormats = _addFormats as unknown as typeof _addFormats.default;

// Rewrite references to use local definitions
function rewriteReferences(obj: SchemaNode): SchemaNode {
  if (typeof obj !== 'object' || obj === null) return obj;

  for (const key in obj) {
    const value = obj[key];
    if (value && typeof value === 'object' && '$ref' in value) {
      const refValue = (value as { $ref: string }).$ref;
      if (refValue && refValue.includes('#/components/schemas/Insight/definitions/')) {
        (value as { $ref: string }).$ref = refValue.replace(
          '#/components/schemas/Insight/definitions/',
          '#/definitions/',
        );
      }
    }
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      rewriteReferences(value);
    }
  }
  return obj;
}

describe('user_interactions_latest_summary view', () => {
  it('should correctly summarize user interactions', async () => {
    // Insert test data
    await pgPool.query(`
      INSERT INTO user_interactions (user_id, org_id, insight_id, action_type, action_target_id, action_target_group, action_value, timestamp)
      VALUES
        ('user1', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '1 hour'),
        ('user1', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'like', NOW() - INTERVAL '30 minutes'),
        ('user1', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '15 hour'),
        ('user1', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'like', NOW() - INTERVAL '10 minutes'),
        ('user2', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'like', NOW() - INTERVAL '45 minutes'),
        ('user3', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '30 minutes'),
        ('user4', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '30 minutes'),
        ('user5', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '30 minutes'),
        ('user6', 'org1', 'insight1', 'ai-suggestions-click', '1', 'immediate', 'dislike', NOW() - INTERVAL '30 minutes')
    `);

    // Query the view
    const { rows } = await pgPool.query(
      "SELECT * FROM user_interactions_latest_summary WHERE insight_id = 'insight1' AND action_type = 'ai-suggestions-click'",
    );

    const likes = rows.find((row: SchemaNode) => row.action_value === 'like')?.interaction_count;
    const dislikes = rows.find(
      (row: SchemaNode) => row.action_value === 'dislike',
    )?.interaction_count;

    expect(likes).toEqual(2);
    expect(dislikes).toEqual(4);

    expect(rows).toEqual([
      {
        insight_id: 'insight1',
        action_type: 'ai-suggestions-click',
        action_target_id: '1',
        action_target_group: 'immediate',
        action_value: 'dislike',
        interaction_count: 4,
        org_id: 'org1',
      },
      {
        insight_id: 'insight1',
        action_type: 'ai-suggestions-click',
        action_target_id: '1',
        action_target_group: 'immediate',
        action_value: 'like',
        interaction_count: 2,
        org_id: 'org1',
      },
    ]);
  });
});

describe('airflow insight', () => {
  it('should validate a ticket schema using json-schema', async () => {
    const schemaDeepCopy = JSON.parse(JSON.stringify(SCHEMA));
    const ajv = new Ajv({ $data: true });
    addFormats(ajv);

    rewriteReferences(schemaDeepCopy);
    const validate = ajv.compile(schemaDeepCopy);
    const valid = validate(insight);
    const errors = validate.errors;

    expect(errors).toEqual(null);
    expect(valid).toEqual(true);
  });

  it('should insert a test ticket into the db', async () => {
    const insightDbFormat = {
      ...insight,
      funnelMetrics: JSON.stringify(insight.funnelMetrics),
      hypotheses: JSON.stringify(insight.hypotheses),
      aiActions: JSON.stringify(insight.aiActions),
      keyMetric: JSON.stringify(insight.keyMetric),
      breakdownDimension: JSON.stringify(insight.breakdownDimension),
    };

    await kysely.insertInto('insights').values(insightDbFormat).execute();

    const insertedInsight = await kysely
      .selectFrom('insights')
      .selectAll()
      .where('id', '=', insight.id)
      .executeTakeFirst();

    expect(insertedInsight).toBeDefined();
    expect(insertedInsight?.id).toEqual(insight.id);
  });

  it('should use the index for a typical FE query', async () => {
    const orgId = 'test_org_id';
    const visible = true;
    const sensitivity = 'high';

    const explainResult = await kysely
      .selectFrom('insights')
      .select(['id', 'incident_time_stamp'])
      .where('org_id', '=', orgId)
      .where('visible', '=', visible)
      .where('sensitivity', '=', sensitivity)
      .orderBy('incident_time_stamp', 'desc')
      .explain('json', sql`analyze`);

    const plan = explainResult[0]['QUERY PLAN'][0].Plan;
    const indexName = plan['Index Name'];

    expect(explainResult.length).toEqual(1);
    expect(explainResult[0]['QUERY PLAN'].length).toEqual(1);
    expect(indexName).toEqual('insights_org_id_sensitivity_visible_incident_time_stamp_index');
  });
});

import { forecastExample, forecastSchema } from '../contracts/revenueForecast.js';

describe('revenue forecast reports', () => {
  it('should validate and insert a revenue forecast report', async () => {
    const ajv = new Ajv({ $data: true });
    addFormats(ajv);

    const validate = ajv.compile(forecastSchema);
    const valid = validate(forecastExample);
    const errors = validate.errors;

    expect(errors).toEqual(null);
    expect(valid).toEqual(true);

    const forecastDbFormat = {
      ...forecastExample,
      key_metric: JSON.stringify(forecastExample.key_metric),
      revenue_groups: JSON.stringify(forecastExample.revenue_groups),
    };

    await kysely.insertInto('revenue_forecast_reports').values(forecastDbFormat).execute();

    const insertedForecast = await kysely
      .selectFrom('revenue_forecast_reports')
      .selectAll()
      .where('executionId', '=', forecastExample.execution_id)
      .executeTakeFirst();

    expect(insertedForecast).toBeDefined();
    expect(insertedForecast?.executionId).toEqual(forecastExample.execution_id);
  });
});
