{"name": "insights-app-be", "version": "1.0.0", "description": "This is a Node.js app acting as the backend for the insights-app-fe.", "main": "index.js", "type": "module", "private": true, "engines": {"node": ">=20.11.0"}, "scripts": {"build": "rimraf dist && npx tsc -p tsconfig.build.json && npx tsc-alias", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "TESTCONTAINERS_RYUK_DISABLED=true TZ=UTC NODE_OPTIONS=--experimental-vm-modules jest", "test-some": "TESTCONTAINERS_RYUK_DISABLED=true TZ=UTC NODE_OPTIONS=--experimental-vm-modules jest -t", "lint": "eslint . --cache --fix", "migrate": "node-pg-migrate", "dev": "tsx watch src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ssm": "^3.716.0", "@langchain/core": "0.3.36", "@langchain/groq": "0.1.3", "@langchain/langgraph": "0.2.41", "@langchain/openai": "0.4.2", "@testcontainers/redis": "10.9.0", "ajv": "8.17.1", "ajv-formats": "3.0.1", "axios": "^1.7.9", "axios-retry": "^4.5.0", "compression": "^1.7.5", "cors": "^2.8.5", "dayjs": "1.11.11", "dotenv": "^16.4.1", "express": "^4.21.2", "express-jwt-authz": "^2.4.1", "express-oauth2-jwt-bearer": "^1.6.0", "flatted": "^3.3.2", "jsonwebtoken": "9.0.2", "kysely": "0.27.5", "langfuse-langchain": "3.35.1", "lodash": "4.17.21", "microdiff": "1.4.0", "node-cache": "^5.1.2", "openai": "4.29.0", "pg": "8.13.1", "pg-pool": "3.7.0", "redis": "4.7.0", "serverless-http": "^3.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "3.17.0"}, "devDependencies": {"@eslint/js": "9.17.0", "@jest/globals": "^29.7.0", "@testcontainers/postgresql": "10.9.0", "@types/aws-lambda": "8.10.146", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/jest": "29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.14", "@types/node": "22.10.2", "@types/pg": "^8.6.6", "@types/redis": "^4.0.11", "@types/supertest": "6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "eslint": "9.17.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "express-list-endpoints": "^7.1.1", "globals": "15.14.0", "jest": "29.7.0", "node-pg-migrate": "7.9.1", "prettier": "^3.5.1", "rimraf": "6.0.1", "supertest": "^6.3.3", "testcontainers": "10.9.0", "ts-jest": "29.2.6", "ts-jest-resolver": "2.0.1", "tsx": "4.19.4", "typescript": "5.7.2", "typescript-eslint": "8.18.2"}}