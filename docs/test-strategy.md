# Test Strategy for Insights App Backend (Lambda)

## 1. Objectives

- Ensure the reliability and correctness of individual units of code
- Verify the proper functioning of Express.js routes, controllers, and middleware
- Improve code quality and catch bugs early in the development process
- Facilitate easier refactoring and maintenance of the codebase
- Ensure smooth integration with external services (PostgreSQL, Redis, OpenAI)
- Verify correct handling of AWS Lambda events

## 2. Testing Frameworks and Tools

- Jest: For unit testing and integration testing
- Supertest: For API endpoint testing
- AWS SDK Mock: For mocking AWS services in tests
- Docker: For running integration tests with actual database and Redis instances

## 3. Unit Testing Strategy

- Focus on testing utility functions in the `/src/common/utils` directory
- Use Jest for writing and running unit tests
- Aim for high code coverage (e.g., 80% or higher) for utility functions
- Test edge cases and error handling
- Mock external dependencies (database, Redis, OpenAI) for isolated testing

## 4. Integration Testing Strategy

- Test the interaction between different components and external services
- Use Jest and Supertest for API endpoint testing
- Set up a test database and Redis instance using Docker for integration tests
- Test database operations, Redis caching, and OpenAI service interactions
- Verify correct handling of transactions and connection management

### Lambda Handler Testing Strategy

- Test the AWS Lambda handler function in `src/index.js`
- Use AWS SDK Mock to simulate Lambda events and context
- Test different event types (e.g SQS) to ensure correct routing
- Check out how the SRA repo does it
- Verify proper initialization of the Express app within the Lambda context

### Controller Testing Strategy

- Focus on testing controller functions in `src/http/controllers`
- Use Jest and Supertest to simulate HTTP requests
- Test different scenarios: successful requests, error handling, edge cases
- Mock database queries and external service calls for controlled testing
- Verify correct response formats and status codes

## 7. Middleware Testing Strategy

- Test custom middleware functions
- Verify proper request processing and modification
- Test error handling and next() function calls
- Ensure middleware correctly integrates with routes and controllers

## 8. End-to-End Testing Strategy

- Use Jest and Supertest for end-to-end API testing
- Focus on critical user flows and key features
- Test complete request-response cycles, including database and cache interactions
- Verify correct handling of authentication and authorization
- Test integration with all external services (PostgreSQL, Redis, OpenAI)

## 9. Test Data Management

- Create a set of mock data for database records, cache entries, and API responses
- Use consistent test data across unit, integration, and E2E tests
- Store mock data in a separate directory (e.g., `test/fixtures`)
- Use factories or fixtures to generate test data programmatically when needed

## 10. Continuous Integration

- Run all unit, integration, and E2E tests on every pull request
- Set up automated test runs on the existing CI/CD platform
- Block merging of pull requests if tests fail
- Implement parallel test execution to reduce CI build times

## 11. Maintenance and Refactoring

- Update tests whenever related code changes
- Regularly review and update the test suite to ensure it remains relevant and effective
- Refactor tests to improve readability and maintainability

## 12. Test Organization and File Structure

- Jest tests:

  - Place test files in a `__tests__` directory within the directory of the source files they're testing
  - Use the naming convention `[filename].test.js`
  - Example: `/src/common/utils/__tests__/pgConn.test.js` for testing `/src/common/utils/pgConn.js`

- Integration tests:

  - Keep integration tests in a separate `/test/integration/` directory
  - Organize tests by feature or service
  - Use descriptive names like `search.test.js`, `auth.test.js`, etc.

- E2E tests:

  - Store E2E tests in a `/test/e2e/` directory
  - Organize tests by API endpoint or user flow
  - Use names that describe the flow, e.g., `searchFlow.test.js`

- Fixtures and helpers:
  - Store mock data and test helpers in `/test/fixtures/` and `/test/helpers/` respectively

## 13. Best Practices

- Write descriptive test names that explain the expected behavior
- Use beforeEach and afterEach hooks to set up and tear down test environments
- Mock external dependencies and API calls when appropriate
- Keep tests independent and avoid dependencies between test cases
- Follow the Arrange-Act-Assert (AAA) pattern in test structure
- Group related tests using describe blocks
- Use environment variables for configuration in tests
- Implement proper error handling and assertions for async operations
