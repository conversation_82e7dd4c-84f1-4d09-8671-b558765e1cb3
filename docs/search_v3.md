# Search V3 Technical Documentation

## Overview

Search V3 is an advanced search system built on a graph-based architecture that processes natural language queries and returns relevant results. It combines SQL-based filtering with semantic search capabilities to provide accurate and contextually relevant results.

The system is designed to handle complex queries by breaking them down into components that can be processed by specialized agents, then combining the results to create a comprehensive search experience.

## Architecture

Search V3 is built using a directed graph architecture powered by LangGraph, where each node represents a specific processing step. The main components are:

1. **Text-to-SQL Conversion**: Transforms natural language queries into SQL components
2. **Base Query Builder**: Constructs the foundation SQL query
3. **Semantic/Non-Semantic Path**: Determines and executes the appropriate search strategy. It includes two sub-paths:
   - **Non-Semantic Path**: Directly executes SQL queries for simple queries
   - **Semantic Path**: Utilizes semantic search for complex queries

### Main Graph Structure

```
START → text_to_sql → base_query_builder → [Conditional: isSemantic]
                                         → true → semantic_search → END
                                         → false → non_semantic_fetch → END
```

### Full Graph Structure

[![Search V3 Graph](./assets/search_v3/search_v3_architecture.png)](./assets/search_v3/search_v3_architecture.png)

The semantic pagination subgraph algorithm is the same with the previous versions. A walkthrough can be found [here](./search.md).

## Key Components

### 1. Text-to-SQL Conversion

The text-to-SQL conversion is handled by a subgraph that processes the user query through multiple specialized agents in parallel:

- **Basic Filtering**: Extracts basic filtering conditions from the query. These include the main structured fields of the database schema that can be queried directly, plus that have been flattened into the `insights_search` view.
- **Funnel**: Processes funnel-related query components. This component utilized the `insights_funnel` view that flattens the funnel metrics and allows for fully dynamic steps.
- **Hypotheses**: Handles hypothesis-related query components. Similar to the previous steps, this component utilizes the `insights_hypotheses` view.
- **Semantic Query**: Determines if semantic search is needed, extracts the semantic portion, and runs a vector similarity search to the relevant DB table.
- **Sort/Limit**: Extracts sorting and limiting parameters.

Each agent produces "partials" - fragments of SQL components that will be combined later.

### 2. Base Query Builder

Takes the partials from the text-to-SQL conversion and constructs a base SQL query using Kysely, a type-safe SQL query builder. This query serves as the foundation for both semantic and non-semantic search paths, and also for the counting query that returns the total number of results the specified filters would return.

### 3. Semantic Search Path

If the query requires semantic understanding (determined by the presence of a semantic query component), the system:

1. Generates embeddings for the semantic portion of the query using OpenAI's embedding model.
2. Retrieves a pool of candidate results based on vector similarity.
3. Uses an LLM-based results evaluator to rank and filter the candidates.
4. Returns the most relevant results for the current page.

### 4. Non-Semantic Search Path

For queries that can be satisfied with direct SQL filtering:

1. Executes the base query with appropriate sorting and pagination
2. Returns the results directly without additional LLM evaluation

## Database Schema

Search V3 utilizes several database views created specifically for efficient searching:

- **insights_search**: Main view containing flattened insight data
- **insights_search_hypotheses**: View for hypothesis-related data
- **insights_search_funnel**: View for funnel metrics data

These views are designed to simplify querying for the LLM agents.

## Implementation Details

### LLM Models

Search V3 uses the following LLM models:

- **llama-3.3-70b-versatile** (via Groq): For text-to-SQL conversion and results evaluation
- **OpenAI Embeddings**: For generating vector embeddings for semantic search

### Caching

Results are cached at multiple levels:

- Agent calls are cached with Redis for 1 hour to make navigation in a single session faster.
- Pagination offsets are cached to improve the performance of navigating through pages of the same query.

## Differences from Search V1/V2

Search V3 introduces several improvements over the previous version:

1. **Parallel Agent Processing**: Instead of a single call to a large model (previously GPT-4o), multiple specialized agents process the query in parallel, each assigned with generating specific SQL components.
2. **Database Views**: Views make querying less complex for the LLM agents, and can be easily extended to include any parts of the Insight, no matter how nested.
3. **Reduced Pool Size**: More efficient candidate pool evaluation. In v2 the pool size was 60 and it was processed by a single call to a larger model (GPT-4o). In v3 the pool size is 45 and it's processed by parallel calls to a smaller model (llama-3.3-70b-versatile), each one processing 15 candidates.

## API Usage

Calling the search_v3 graph requires the following input:

```typescript
interface Query {
  userQuery: string;  // Natural language query
  orgId: string;      // Organization ID
  page?: number;      // Page number (default: 1)
}

interface Metadata {
  userEmail: string;  // User email for tracking
  env: string;        // Environment
  sessionId: string;  // Session ID for tracking
}
```

And returns the following:

```typescript
interface SearchResponse {
  insights: Insight[];       // Array of insight objects
  filteredCount: number;     // Number of insights after filtering
}
```

## Example Flow

1. User submits a query: "Show me the 3 most important revenue anomalies from last week"
2. Text-to-SQL agents process the query in parallel:
   - Basic filtering detects the "last week" condition, which is mapped to the incident_time_stamp field
   - Sort/limit determines to sort by `revenue_delta` (the default metric for importance) in descending order, and limit to 3 results
   - The rest of the agents return empty partials
3. Base query builder constructs the SQL query
4. System determines this is a non-semantic query (no semantic component)
5. Non-semantic fetch executes the SQL query directly
6. Results are returned to the user

For a semantic query like "Show me insights about checkout abandonment issues":

1. Semantic query agent identifies "checkout abandonment issues" as requiring semantic search
2. System generates embeddings for this phrase
3. Vector search retrieves candidate insights
4. Results evaluator ranks candidates based on relevance
5. Most relevant results are returned

## Monitoring and Telemetry

Search V3 integrates with Langfuse for monitoring and telemetry:

- Records user search queries along with metadata information (user email, environment, session ID)
- Tracks all agent calls and their performance
- Provides insights into overall system performance and usage patterns

## Challenges and Future Improvements

This approach to search presents some challenges:

1. Smaller agents, while capable and faster, can make mistakes when its not very clear what the user is asking for, or how the request can be fulfilled. This can only be mitigated by comprehensive examples.
2. The agent that determines the semantic part of the query must determine which part of the query can not be served by all the other agents. This means that its prompt must include all the information in which the other agents act upon to determine their own part of the query. This could be proved to be challenging and not scalable.
3. Semantic search is currently slow and the bottleneck seems to be the SQL query execution. This could probably be improved by DB optimizations (like indexes, or converting virtual views to materialized views).

## Preview

Search v3 can be previewed at https://pr-239.insights.test.baresquare.com/revenue-insights/
