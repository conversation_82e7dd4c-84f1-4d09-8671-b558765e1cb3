{"info": {"_postman_id": "8403c0a4-9fe5-4823-9dd7-74641dbc9dc2", "name": "Insights App BE", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "18894505"}, "item": [{"name": "Insights", "item": [{"name": "Get Insight", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/1", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "1"]}}, "response": []}, {"name": "Patch Insight", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"visible\":false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/1", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "1"]}}, "response": []}, {"name": "Get Org Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_U5eGLkpe6H7zXQGN/insights?visible=true&skip=5&limit=3&sensitivity=low,medium", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_U5eGLkpe6H7zXQGN", "insights"], "query": [{"key": "visible", "value": "true"}, {"key": "skip", "value": "5"}, {"key": "limit", "value": "3"}, {"key": "sensitivity", "value": "low,medium"}]}}, "response": []}, {"name": "Get Org Insights Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_U5eGLkpe6H7zXQGN/insights/count?visible=true&sensitivity=low,medium", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_U5eGLkpe6H7zXQGN", "insights", "count"], "query": [{"key": "visible", "value": "true"}, {"key": "sensitivity", "value": "low,medium"}]}}, "response": []}, {"name": "Delete Insight", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/1", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "1"]}}, "response": []}, {"name": "Get Insights", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights?orgIds=org_XDapSbcrSIi2BLxy&startDate=2024-12-21&endDate=2024-12-21", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights"], "query": [{"key": "orgIds", "value": "org_XDapSbcrSIi2BLxy"}, {"key": "startDate", "value": "2024-12-21"}, {"key": "endDate", "value": "2024-12-21"}]}}, "response": []}, {"name": "Insights Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_U5eGLkpe6H7zXQGN/insights/search?q=fetch insights from october&page=1", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_U5eGLkpe6H7zXQGN", "insights", "search"], "query": [{"key": "q", "value": "fetch insights from october"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Create Insight", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{insights_app_be_host}}/api/v1/insights", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights"]}}, "response": []}]}, {"name": "User Interactions", "item": [{"name": "Insight Interactions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/69697/user_interactions/ai_suggestions/latest", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "69697", "user_interactions", "ai_suggestions", "latest"]}}, "response": []}, {"name": "Insight Interactions Summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/69697/user_interactions/ai_suggestions/summary", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "69697", "user_interactions", "ai_suggestions", "summary"]}}, "response": []}, {"name": "Get Org UIs", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_XDapSbcrSIi2BLxy/user_interactions/ai_suggestions/latest", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_XDapSbcrSIi2BLxy", "user_interactions", "ai_suggestions", "latest"]}}, "response": []}, {"name": "AI Suggestions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/insights/user_interactions/ai_suggestions/latest?startDate=2025-01-01&endDate=2025-01-09&insightIds=69686", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "insights", "user_interactions", "ai_suggestions", "latest"], "query": [{"key": "startDate", "value": "2025-01-01"}, {"key": "endDate", "value": "2025-01-09"}, {"key": "insightIds", "value": "69686"}]}}, "response": []}]}, {"name": "Forecasts", "item": [{"name": "Get Org Forecasts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_e0uCMD8XeCuYkxZL/forecasts", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_e0uCMD8XeCuYkxZL", "forecasts"]}}, "response": []}, {"name": "Get Org Forecast", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_e0uCMD8XeCuYkxZL/forecasts/5", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_e0uCMD8XeCuYkxZL", "forecasts", "5"]}}, "response": []}, {"name": "Get Org Latest Forecast", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{insights_app_be_host}}/api/v1/organizations/org_e0uCMD8XeCuYkxZL/forecasts/latest", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "organizations", "org_e0uCMD8XeCuYkxZL", "forecasts", "latest"]}}, "response": []}, {"name": "Create Forecast", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{insights_app_be_host}}/api/v1/forecasts", "host": ["{{insights_app_be_host}}"], "path": ["api", "v1", "forecasts"]}}, "response": []}]}]}