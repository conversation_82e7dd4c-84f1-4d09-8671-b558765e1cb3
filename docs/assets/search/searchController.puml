@startuml Search Controller
skinparam sequenceGroupBodyBackgroundColor transparent
skinparam SequenceGroupBorderColor #888888
skinparam SequenceGroupBorderThickness 5
skinparam SequenceGroupHeaderFontStyle bold
skinparam Padding 3

participant "search" as search
participant "validateInput" as validateInput
participant "getNonSemanticResults" as getNonSemanticResults
participant "getNonSemanticCount" as getNonSemanticCount
participant "getPageOfSemanticResults" as getPageOfSemanticResults
participant "getTotalCount" as getTotalCount

box "searchController.js" #FFEEEE
    participant search
    participant validateInput
    participant getNonSemanticResults
    participant getNonSemanticCount
    participant getPageOfSemanticResults
    participant getTotalCount
end box

box "openAIService.js" #FFEEFF
    participant "callAgentWithCache" as callAgentWithCache
end box

box "queryBuilderAgent.js" #EEFFEE
    participant "queryBuilderAgent" as queryBuilderAgent
end box

box "resultsEvaluatorAgent.js" #EEEEFF
    participant "resultsEvaluatorAgent" as resultsEvaluatorAgent
end box

box "semanticPagination.js" #FFFFEE
    participant "findPoolOffset" as findPoolOffset
end box

box "pgConn.js" #EEFFFF
    participant "query" as postgresQuery
end box

[-> search: HTTP Request
activate search

search -> validateInput: Validate input
activate validateInput
validateInput --> search: Input valid
deactivate validateInput

search -> callAgentWithCache: Get query elements
activate callAgentWithCache
callAgentWithCache -> queryBuilderAgent: Build query
activate queryBuilderAgent
queryBuilderAgent --> callAgentWithCache: Query elements
deactivate queryBuilderAgent
callAgentWithCache --> search: Query elements
deactivate callAgentWithCache

alt No semantic query
    search -> getNonSemanticResults: Get results
    activate getNonSemanticResults
    getNonSemanticResults -> postgresQuery: Execute query
    postgresQuery --> getNonSemanticResults: Query results
    getNonSemanticResults --> search: Non-semantic results
    deactivate getNonSemanticResults

    search -> getNonSemanticCount: Get count
    activate getNonSemanticCount
    getNonSemanticCount -> postgresQuery: Execute count query
    postgresQuery --> getNonSemanticCount: Count result
    getNonSemanticCount --> search: Non-semantic count
    deactivate getNonSemanticCount
else Semantic query
    search -> findPoolOffset: Find pool offset
    activate findPoolOffset
    findPoolOffset --> search: Pool offset
    deactivate findPoolOffset

    search -> getPageOfSemanticResults: Get semantic results
    activate getPageOfSemanticResults
    getPageOfSemanticResults -> postgresQuery: Execute semantic query
    postgresQuery --> getPageOfSemanticResults: Query results
    getPageOfSemanticResults -> callAgentWithCache: Evaluate results
    activate callAgentWithCache
    callAgentWithCache -> resultsEvaluatorAgent: Evaluate
    activate resultsEvaluatorAgent
    resultsEvaluatorAgent --> callAgentWithCache: Evaluated results
    deactivate resultsEvaluatorAgent
    callAgentWithCache --> getPageOfSemanticResults: Evaluated results
    deactivate callAgentWithCache
    getPageOfSemanticResults --> search: Semantic results
    deactivate getPageOfSemanticResults

    search -> getTotalCount: Get total count
    activate getTotalCount
    getTotalCount --> search: Total count
    deactivate getTotalCount
end

[<-- search: JSON Response
deactivate search

@enduml
