# Working with submodules

## Fetching the submodules of a repo

When cloning a repo for the first time, the submodules can be automatically included using the `--recurse-submodules` flag:

```
git clone --recurse-submodules https://github.com/your-username/insights-app-be.git
```

If you already have cloned the project locally without the submodules, and you now need to fetch them, use:

```
git submodule update --init --recursive
```

## How to see the current version of a submodule

Submodules pinpoint to a specific version (commit) of the submodule repository.

```
git ls-tree HEAD contracts/revenue-plan-forecasting
```

## How to update a submodule to its latest version

### 1. Navigate to the submodule directory and fetch the latest changes:

```
cd contracts/revenue-plan-forecasting
git fetch
git checkout main
git pull origin main
cd ../..
```

Alternatively, to checkout a specific commit:

```
git checkout <commit-hash>
```

### 2. Stage the Submodule Change

Submodules store the information about the specific commit to which the submodule points, via a special entry called a `gitlink` (see [more](https://git-scm.com/docs/gitsubmodules)). The `gitlink` will appear in the repo status as a file in the same path and name as the submodule folder. In order to save the new submodule version, you will have to commit this pseudo-file:

```
git add contracts/revenue-plan-forecasting
```

Then commit and push the change.
