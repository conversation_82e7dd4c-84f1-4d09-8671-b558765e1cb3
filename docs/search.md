# Search function walk-through

[![Search Controller Sequence Diagram](./assets/search/C4_Code_SearchController.png)](./assets/search/C4_Code_SearchController.png)
_Sequence Diagram of the Search Controller function calls. It's not as complicated as it seems at first sight._

## Algorithm

The search function, located at `src/http/controllers/searchController.js`, consists of two or three steps, depending on whether a semantic search query is necessary.

The first step is common in both cases. In this step, the Natural Language user query is sent to an LLM model, and an object, called Elements, is returned. The Elements provide all the information to construct the SQL query and decide whether a semantic query is necessary.

_In the sequence diagram, this first step is depicted as a call to the `queryBuilderAgent`, right before the `alt` branching._

### Non-Semantic query

If the user query does not require a semantic lookup, the second and final step is executed, with one SQL query fetching the actual data (Insights), and another counting the total number of results.

### Semantic query

Semantic queries are a bit more complex. There is still an SQL query, which returns Insights ordered by their semantic similarity to the user query, but, since this ordering is not highly accurate, the SQL results are treated as a pool of candidates, instead of final results. In a second step, these candidates are filtered by a new LLM call. This is called a two-stage retrieveal. In our current setting, where the page size is set to 20, the candidates pool size is set to 60. This number is selected as a guarantee that, if there are at least enough relevant results, at least 20 of them will appear in the pool.

#### The pagination algorithm

[![Search Controller Sequence Diagram](./assets/search/paginationAlgorithm.png)](./assets/search/paginationAlgorithm.png)
_The next pool starts where the previous page filtered results end_

Before we can run the SQL query, we need to know the _offset_, that is, where our pool of candidates starts. If we are in the first page, the matter is simple, the offset is 0. If we are not in the first page, the matter is also simple, kinda: the offset is where the previous page's final results end. As is already implied, a recursive function, called `findPoolOffset`, is used to perform this task. The offsets are cached, and the function goes back until either there is a cache hit, or it reaches page 1.

Once we get the offset, we can run the SQL query and get our pool of candidates. The final step is to provide this pool to the second LLM agent, that will filter out irrelevant insights and provide the final results. These two steps are implemented by the function `getPageOfSemanticResults`. That's it!
